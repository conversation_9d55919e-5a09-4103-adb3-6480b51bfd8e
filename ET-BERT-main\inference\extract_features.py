import sys
import os
import torch
import argparse
import collections
import torch.nn as nn
import numpy as np

uer_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.append(uer_dir)
sys.path.append(os.path.join(uer_dir, 'fine-tuning'))
from uer.utils.constants import *
from uer.utils import *
from uer.utils.config import load_hyperparam
from uer.utils.seed import set_seed
from uer.model_loader import load_model
from uer.opts import infer_opts
from run_classifier import Classifier


class FeatureExtractor(Classifier):
    def __init__(self, args):
        super(FeatureExtractor, self).__init__(args)

    def forward(self, src, seg):
        emb = self.embedding(src, seg)
        output = self.encoder(emb, seg)
        
        features = output[:, 0, :]
        
        logits = self.output_layer_2(torch.tanh(self.output_layer_1(features)))
        return features, logits


def batch_loader(batch_size, src, seg):
    instances_num = src.size()[0]
    for i in range(instances_num // batch_size):
        src_batch = src[i * batch_size : (i + 1) * batch_size, :]
        seg_batch = seg[i * batch_size : (i + 1) * batch_size, :]
        yield src_batch, seg_batch
    if instances_num > instances_num // batch_size * batch_size:
        src_batch = src[instances_num // batch_size * batch_size :, :]
        seg_batch = seg[instances_num // batch_size * batch_size :, :]
        yield src_batch, seg_batch


def read_dataset(args, path):
    dataset, columns = [], {}
    with open(path, mode="r", encoding="utf-8") as f:
        for line_id, line in enumerate(f):
            if line_id == 0:
                line = line.strip().split("\t")
                for i, column_name in enumerate(line):
                    columns[column_name] = i
                continue
            line = line.strip().split("\t")
            if "text_b" not in columns:
                text_a = line[columns["text_a"]]
                src = args.tokenizer.convert_tokens_to_ids([CLS_TOKEN] + args.tokenizer.tokenize(text_a))
                seg = [1] * len(src)
            else:
                text_a, text_b = line[columns["text_a"]], line[columns["text_b"]]
                src_a = args.tokenizer.convert_tokens_to_ids([CLS_TOKEN] + args.tokenizer.tokenize(text_a) + [SEP_TOKEN])
                src_b = args.tokenizer.convert_tokens_to_ids(args.tokenizer.tokenize(text_b) + [SEP_TOKEN])
                src = src_a + src_b
                seg = [1] * len(src_a) + [2] * len(src_b)
            
            if len(src) > args.seq_length:
                src = src[: args.seq_length]
                seg = seg[: args.seq_length]
            while len(src) < args.seq_length:
                src.append(0)
                seg.append(0)
            dataset.append((src, seg))

    return dataset


def main():
    parser = argparse.ArgumentParser(formatter_class=argparse.ArgumentDefaultsHelpFormatter)

    infer_opts(parser)

    parser.add_argument("--pooling", choices=["mean", "max", "first", "last"], default="first",
                        help="Pooling type.")

    parser.add_argument("--labels_num", type=int, required=True,
                        help="Number of prediction labels.")

    parser.add_argument("--tokenizer", choices=["bert", "char", "space"], default="bert",
                        help="Specify the tokenizer.")

    parser.add_argument("--feature_path", type=str, required=True,
                        help="Path to the output feature file.")

    args = parser.parse_args()
    args = load_hyperparam(args)
    args.tokenizer = str2tokenizer[args.tokenizer](args)
    
    args.soft_targets, args.soft_alpha = False, False
    model = FeatureExtractor(args)
    model = load_model(model, args.load_model_path)

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model = model.to(device)
    if torch.cuda.device_count() > 1:
        model = torch.nn.DataParallel(model)

    dataset = read_dataset(args, args.test_path)
    src = torch.LongTensor([sample[0] for sample in dataset])
    seg = torch.LongTensor([sample[1] for sample in dataset])

    batch_size = args.batch_size
    instances_num = src.size()[0]

    print("The number of prediction instances: ", instances_num)

    model.eval()

    all_features = []
    all_preds = []

    # Create the output directory if it doesn't exist
    output_dir = os.path.dirname(args.prediction_path)
    if not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)
    
    feature_dir = os.path.dirname(args.feature_path)
    if not os.path.exists(feature_dir):
        os.makedirs(feature_dir, exist_ok=True)

    with open(args.prediction_path, "w", encoding="utf-8") as f_pred:
        f_pred.write("label\n")
        for i, (src_batch, seg_batch) in enumerate(batch_loader(batch_size, src, seg)):
            src_batch = src_batch.to(device)
            seg_batch = seg_batch.to(device)
            with torch.no_grad():
                features, logits = model(src_batch, seg_batch)
            
            pred = torch.argmax(logits, dim=1)
            
            all_features.append(features.cpu().numpy())
            all_preds.extend(pred.cpu().numpy().tolist())

            for p in pred.cpu().numpy():
                f_pred.write(str(p) + "\n")

    all_features = np.concatenate(all_features, axis=0)
    np.save(args.feature_path, all_features)

    print(f"Features saved to {args.feature_path}")
    print(f"Predictions saved to {args.prediction_path}")


if __name__ == "__main__":
    main() 