import pandas as pd
import argparse

def analyze_clusters(file_path):
    """
    Analyzes the clustered dataset to show the relationship between
    true labels and cluster labels.
    """
    try:
        df = pd.read_csv(file_path, sep='\t')
    except FileNotFoundError:
        print(f"Error: The file '{file_path}' was not found.")
        return

    # --- Analysis 1: From the perspective of each TRUE APPLICATION ---
    print("--- Analysis 1: How each True Application is distributed among clusters ---")
    print("This shows if an application's traffic is concentrated or scattered.\n")

    # Group by the true application name and show the distribution of its samples into different clusters
    app_distribution = df.groupby('ground_truth_name')['cluster_label'].value_counts(normalize=True).mul(100).round(1).astype(str) + '%'
    
    for app_name in df['ground_truth_name'].unique():
        print(f"--- Application: {app_name} ---")
        distribution = app_distribution[app_name]
        # Show top 5 clusters for each app for brevity
        print(distribution.head(5).to_string())
        if len(distribution) > 5:
            print(f"  ... and {len(distribution) - 5} more clusters.")
        print("-" * (len(app_name) + 20) + "\n")


    # --- Analysis 2: From the perspective of each CLUSTER ---
    print("\n\n--- Analysis 2: The composition of each discovered Cluster ---")
    print("This shows the purity of each cluster and what application it represents.\n")
    
    # Group by the cluster label and show the distribution of true applications within it
    cluster_composition = df.groupby('cluster_label')['ground_truth_name'].value_counts(normalize=True).mul(100).round(1).astype(str) + '%'

    for cluster_id in sorted(df['cluster_label'].unique()):
        print(f"--- Cluster: {cluster_id} ---")
        composition = cluster_composition[cluster_id]
        # Show top 5 apps in each cluster
        print(composition.head(5).to_string())
        if len(composition) > 5:
            print(f"  ... and {len(composition) - 5} more applications.")
        print("-" * (len(str(cluster_id)) + 16) + "\n")
        
    
    # --- Analysis 3: Summary Mapping ---
    print("\n\n--- Analysis 3: Dominant Application per Cluster (Mapping Summary) ---")
    print("This provides a direct mapping from our discovered cluster to a real application.\n")
    
    # Find the dominant application for each cluster
    dominant_apps = df.groupby('cluster_label')['ground_truth_name'].agg(lambda x: x.value_counts().index[0])
    
    print(dominant_apps.to_string())


def main():
    parser = argparse.ArgumentParser(description="Analyze the mapping between true labels and cluster labels.")
    parser.add_argument("--clustered_path", type=str, required=True,
                        help="Path to the .tsv file containing the data with cluster labels.")
    args = parser.parse_args()
    
    analyze_clusters(args.clustered_path)


if __name__ == "__main__":
    main() 