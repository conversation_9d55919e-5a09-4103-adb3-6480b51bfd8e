#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行改进的ET-BERT对比学习
"""

import subprocess
import sys
import os

def main():
    print("🚀 开始改进的ET-BERT对比学习")
    print("=" * 60)
    
    try:
        # 步骤1: 运行改进的对比学习训练
        print("🎯 步骤1: 改进的ET-BERT对比学习训练...")
        cmd1 = [
            sys.executable, "train_contrastive_bert_improved.py",
            "--input_features_path", "results/evaluated_test_features_sequence.tsv",
            "--pretrained_model_path", "ET-BERT-main/models/pre-trained_model.bin",
            "--vocab_path", "ET-BERT-main/models/encryptd_vocab.txt",
            "--config_path", "ET-BERT-main/bert_base_config.json",
            "--projection_dim", "10",  # 极小的维度
            "--epochs", "5",  # 更少的epoch
            "--batch_size", "16",  # 稍大的batch size
            "--learning_rate", "5e-5",  # 稍高的学习率
            "--temperature", "0.1",  # 更高的温度
            "--max_seq_length", "128",
            "--min_class_samples", "10"  # 过滤小类别
        ]
        
        print("命令:", " ".join(cmd1))
        print("-" * 60)
        result1 = subprocess.run(cmd1, check=True)
        print("✅ 改进的对比学习训练完成!")
        
        # 步骤2: 运行聚类分析
        print("\n🔍 步骤2: 运行聚类分析...")
        features_file = "results/improved_contrastive_bert_features_d10.tsv"
        
        if not os.path.exists(features_file):
            print(f"❌ 特征文件不存在: {features_file}")
            return False
            
        cmd2 = [
            sys.executable, "ET-BERT-main/cluster_analysis.py",
            "--input_features_path", features_file,
            "--method", "hdbscan",
            "--min_cluster_size", "90",  # 聚类大小设为90
            "--umap_dims", "64",  # 匹配投影维度
            "--output_clustered_path", "results/clustered_improved_contrastive_bert_d64_m90.tsv"
        ]
        
        print("命令:", " ".join(cmd2))
        result2 = subprocess.run(cmd2, check=True)
        print("✅ 聚类分析完成!")
        
        # 步骤3: 比较所有方法的结果
        print("\n📊 步骤3: 方法效果比较")
        print("=" * 60)
        
        import pandas as pd
        
        methods = [
            ("1️⃣  简单神经网络对比学习", "results/clustered_contrastive_unknown_samples_d128_m90.tsv"),
            ("2️⃣  ET-BERT端到端对比学习", "results/clustered_contrastive_tuned_bert_m90.tsv"),
            ("3️⃣  改进的ET-BERT对比学习(10维)", "results/clustered_improved_contrastive_bert_d10_m90.tsv")
        ]
        
        results_summary = []
        
        for method_name, file_path in methods:
            if os.path.exists(file_path):
                df = pd.read_csv(file_path, sep='\t')
                unique_clusters = df['cluster_label'].unique()
                n_clusters = len(unique_clusters)
                n_noise = 0
                
                if -1 in unique_clusters:
                    n_clusters -= 1
                    n_noise = (df['cluster_label'] == -1).sum()
                
                noise_ratio = n_noise / len(df) * 100
                
                print(f"{method_name}:")
                print(f"  📁 文件: {os.path.basename(file_path)}")
                print(f"  📊 样本数: {len(df)}")
                print(f"  🎯 聚类数: {n_clusters}")
                print(f"  🔇 噪声点: {n_noise} ({noise_ratio:.1f}%)")
                
                # 计算聚类大小分布
                cluster_sizes = df[df['cluster_label'] != -1]['cluster_label'].value_counts()
                if len(cluster_sizes) > 0:
                    print(f"  📏 平均聚类大小: {cluster_sizes.mean():.1f}")
                    print(f"  📐 最大聚类大小: {cluster_sizes.max()}")
                    print(f"  📉 最小聚类大小: {cluster_sizes.min()}")
                
                results_summary.append({
                    'method': method_name,
                    'clusters': n_clusters,
                    'noise_ratio': noise_ratio,
                    'avg_cluster_size': cluster_sizes.mean() if len(cluster_sizes) > 0 else 0
                })
                print()
            else:
                print(f"❌ {method_name}: 文件不存在")
                print()
        
        # 总结
        print("=" * 60)
        print("📈 效果对比总结:")
        print("=" * 60)
        for result in results_summary:
            print(f"{result['method']}")
            print(f"   聚类数: {result['clusters']}, 噪声率: {result['noise_ratio']:.1f}%, 平均聚类大小: {result['avg_cluster_size']:.1f}")
        
        print("\n🎉 所有步骤完成!")
        print("=" * 60)
        print("💡 改进要点:")
        print("- 使用了更稳定的InfoNCE损失函数")
        print("- 降低了投影维度到64维")
        print("- 过滤了样本数少于10的类别")
        print("- 使用了梯度裁剪防止梯度爆炸")
        print("- 添加了Dropout和LayerNorm提高泛化能力")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 运行失败，错误代码: {e.returncode}")
        return False
    except Exception as e:
        print(f"❌ 运行出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n💥 运行失败，请检查错误信息")
        sys.exit(1)
