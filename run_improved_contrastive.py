#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行改进的ET-BERT对比学习
"""

import subprocess
import sys
import os

def main():
    print("🚀 开始改进的ET-BERT对比学习")
    print("=" * 60)
    
    try:
        # 步骤1: 运行改进的对比学习训练
        print("🎯 步骤1: 改进的ET-BERT对比学习训练...")
        cmd1 = [
            sys.executable, "train_contrastive_bert_improved.py",
            "--input_features_path", "results/evaluation_results_with_features.tsv",
            "--pretrained_model_path", "ET-BERT-main/models/pre-trained_model.bin",
            "--vocab_path", "ET-BERT-main/models/encryptd_vocab.txt",
            "--config_path", "ET-BERT-main/bert_base_config.json",
            "--projection_dim", "64",  # 极小的维度
            "--epochs", "2",  # 更少的epoch
            "--batch_size", "16",  # 稍大的batch size
            "--learning_rate", "5e-5",  # 稍高的学习率
            "--temperature", "0.1",  # 更高的温度
            "--max_seq_length", "128",
            "--min_class_samples", "10",  # 过滤小类别
            "--output_features_path", "results/improved_contrastive_bert_features_d64.tsv"
        ]
        
        print("命令:", " ".join(cmd1))
        print("-" * 60)
        result1 = subprocess.run(cmd1, check=True)
        print("✅ 改进的对比学习训练完成!")
        
        # 步骤2: 运行聚类分析
        print("\n🔍 步骤2: 运行聚类分析...")
        features_file = "results/improved_contrastive_bert_features_d64.tsv"
        
        if not os.path.exists(features_file):
            print(f"❌ 特征文件不存在: {features_file}")
            return False
            
        cmd2 = [
            sys.executable, "ET-BERT-main/cluster_analysis.py",
            "--input_features_path", features_file,
            "--method", "hdbscan",
            "--min_cluster_size", "90",  # 聚类大小设为90
            "--umap_dims", "64",  # 匹配投影维度
            "--output_clustered_path", "results/clustered_improved_contrastive_bert_d64_m90.tsv"
        ]
        
        print("命令:", " ".join(cmd2))
        result2 = subprocess.run(cmd2, check=True)
        print("✅ 聚类分析完成!")

        print("\n🎉 所有步骤完成!")
        print("=" * 60)
        print("💡 改进要点:")
        print("- 使用了更稳定的InfoNCE损失函数")
        print("- 降低了投影维度到64维")
        print("- 过滤了样本数少于10的类别")
        print("- 使用了梯度裁剪防止梯度爆炸")
        print("- 添加了Dropout和LayerNorm提高泛化能力")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 运行失败，错误代码: {e.returncode}")
        return False
    except Exception as e:
        print(f"❌ 运行出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n💥 运行失败，请检查错误信息")
        sys.exit(1)
