import os
import json
import subprocess
import argparse
from collections import defaultdict

def check_packet_features(packet_layers):
    """
    Checks a single packet's layers for key features and returns a descriptive string.
    
    Args:
        packet_layers (dict): The 'layers' dictionary from tshark's JSON output for a single packet.

    Returns:
        str: A description of the most significant feature found.
    """
    # Check for TLS Client Hello (ssl.handshake.type == 1)
    if 'ssl' in packet_layers and 'ssl.handshake.type' in packet_layers['ssl']:
        if packet_layers['ssl']['ssl.handshake.type'][0] == '1':
            return "  [+] Found TLS Client Hello (Key Feature!)"

    # Check for QUIC Initial Packet (the equivalent of a Client Hello)
    # quic.header_form == 1 (Long Header) and quic.long.packet_type == 0 (Initial)
    if 'quic' in packet_layers:
        if ('quic.header_form' in packet_layers['quic'] and packet_layers['quic']['quic.header_form'][0] == '1' and
            'quic.long.packet_type' in packet_layers['quic'] and packet_layers['quic']['quic.long.packet_type'][0] == '0'):
            return "  [+] Found QUIC Initial Packet (Key Feature!)"
            
    # Check for DNS Query
    if 'dns' in packet_layers and 'dns.qry.name' in packet_layers['dns']:
        query_name = packet_layers['dns']['dns.qry.name'][0]
        return f"  [+] Found DNS Query for: {query_name}"

    # Fallback to basic packet info
    if 'frame' in packet_layers and 'frame.len' in packet_layers['frame']:
        length = packet_layers['frame']['frame.len'][0]
        return f"  [.] Basic L4 Packet (Length: {length} bytes)"
        
    return "  [-] Packet has no recognizable L4+ features."


def analyze_pcap(pcap_path, max_packets_per_stream=5):
    """
    Analyzes a single pcap file using tshark with a robust, two-pass approach.

    Args:
        pcap_path (str): The path to the pcap file.
        max_packets_per_stream (int): The number of initial packets to check per stream.
    """
    print("-" * 80)
    print(f"[*] Analyzing file: {pcap_path}")
    print("-" * 80)

    # --- Pass 1: Probe for existing protocols ---
    # This fast scan checks which high-level protocols are present in the file.
    probe_command = ["tshark", "-r", pcap_path, "-T", "fields", "-e", "_ws.col.protocol"]
    try:
        probe_result = subprocess.run(probe_command, capture_output=True, text=True, check=True, encoding='utf-8', errors='ignore')
        # Get a unique set of all protocols found in the file
        protocols_found = set(probe_result.stdout.strip().split())
    except (subprocess.CalledProcessError, FileNotFoundError):
        # If probing fails, we can't proceed with this file.
        print(f"  [ERROR] Failed to probe protocols in file: {pcap_path}. Skipping.")
        return

    # --- Pass 2: Build a dynamic command and extract data ---
    # Base fields that should always exist
    fields = [
        "-e", "frame.number",
        "-e", "tcp.stream",
        "-e", "udp.stream",
        "-e", "frame.len"
    ]

    # Dynamically add fields based on protocols found in Pass 1
    if 'SSL' in protocols_found or 'TLSv1.2' in protocols_found or 'TLSv1.3' in protocols_found:
        fields.extend(["-e", "ssl.handshake.type"])
    if 'QUIC' in protocols_found:
        fields.extend(["-e", "quic.header_form", "-e", "quic.long.packet_type"])
    if 'DNS' in protocols_found:
        fields.extend(["-e", "dns.qry.name"])

    # Build the final extraction command
    extract_command = ["tshark", "-r", pcap_path, "-T", "json"] + fields
    
    try:
        # Run the dynamically built tshark command
        result = subprocess.run(extract_command, capture_output=True, text=True, check=True, encoding='utf-8', errors='ignore')
        packets_json = json.loads(result.stdout)
    except FileNotFoundError:
        print("\n[ERROR] `tshark` command not found.")
        print("Please ensure Wireshark is installed and `tshark` is in your system's PATH.")
        return
    except subprocess.CalledProcessError as e:
        print(f"\n[ERROR] tshark failed to process the file: {pcap_path}")
        print(f"  tshark stderr: {e.stderr}")
        return
    except json.JSONDecodeError:
        print("\n[INFO] No valid packets found or tshark produced empty/invalid JSON output.")
        return

    # --- The rest of the logic remains the same ---
    streams = defaultdict(list)
    for packet in packets_json:
        if '_source' not in packet or 'layers' not in packet['_source']:
            continue
        
        layers = packet['_source']['layers']
        stream_id = None
        stream_proto = None
        
        # In JSON output, all fields are lists. Need to check existence and content.
        if 'tcp.stream' in layers and layers['tcp.stream'] and layers['tcp.stream'][0] is not None:
            stream_id = layers['tcp.stream'][0]
            stream_proto = "TCP"
        elif 'udp.stream' in layers and layers['udp.stream'] and layers['udp.stream'][0] is not None:
            stream_id = layers['udp.stream'][0]
            stream_proto = "UDP"

        if stream_id is not None:
            streams[f"{stream_proto}_{stream_id}"].append(layers)

    if not streams:
        print("No TCP or UDP streams found in this file.")
        return

    print(f"Found {len(streams)} streams. Checking first {max_packets_per_stream} packets of each...")

    # Analyze the first few packets of each stream
    def sort_key_func(item):
        key_parts = item[0].split('_')
        proto = key_parts[0]
        try: num = int(key_parts[1])
        except (IndexError, ValueError): num = -1
        return (proto, num)

    for stream_key, packet_layers_list in sorted(streams.items(), key=sort_key_func):
        print(f"\n--- Stream: {stream_key} ({len(packet_layers_list)} packets total) ---")
        
        if not packet_layers_list:
            print("  [!] Stream is empty.")
            continue

        for i, packet_layers in enumerate(packet_layers_list[:max_packets_per_stream]):
            # The check_packet_features function needs to handle missing keys gracefully
            # Let's adjust it slightly for max robustness.
            feature_desc = check_packet_features_safe(packet_layers)
            print(f"  Packet {i+1}: {feature_desc}")

        if len(packet_layers_list) < max_packets_per_stream:
            print(f"  (Stream has fewer than {max_packets_per_stream} packets)")

# We also need a "safer" version of the check function that doesn't assume keys exist.
def check_packet_features_safe(packet_layers):
    """
    A safer version of check_packet_features that uses .get() to avoid KeyErrors.
    """
    # Check for TLS Client Hello (ssl.handshake.type == 1)
    ssl_layer = packet_layers.get('ssl', {})
    if ssl_layer and ssl_layer.get('ssl.handshake.type') and ssl_layer['ssl.handshake.type'][0] == '1':
        return "  [+] Found TLS Client Hello (Key Feature!)"

    # Check for QUIC Initial Packet
    quic_layer = packet_layers.get('quic', {})
    if (quic_layer and quic_layer.get('quic.header_form') and quic_layer['quic.header_form'][0] == '1' and
            quic_layer.get('quic.long.packet_type') and quic_layer['quic.long.packet_type'][0] == '0'):
        return "  [+] Found QUIC Initial Packet (Key Feature!)"
            
    # Check for DNS Query
    dns_layer = packet_layers.get('dns', {})
    if dns_layer and dns_layer.get('dns.qry.name'):
        query_name = dns_layer['dns.qry.name'][0]
        return f"  [+] Found DNS Query for: {query_name}"

    # Fallback to basic packet info
    frame_layer = packet_layers.get('frame', {})
    if frame_layer and frame_layer.get('frame.len'):
        length = frame_layer['frame.len'][0]
        return f"  [.] Basic L4 Packet (Length: {length} bytes)"
        
    return "  [-] Packet has no recognizable L4+ features."


def main():
    parser = argparse.ArgumentParser(
        description="Analyze the first 5 packets of each stream in pcap files within a folder.",
        formatter_class=argparse.RawTextHelpFormatter
    )
    parser.add_argument("folder_path", help="Path to the folder containing pcap files.")
    args = parser.parse_args()

    folder_path = args.folder_path
    if not os.path.isdir(folder_path):
        print(f"[ERROR] The provided path is not a valid directory: {folder_path}")
        return

    print(f"[*] Starting scan in folder: {folder_path}\n")

    # Find all pcap and pcapng files
    pcap_files = []
    for filename in os.listdir(folder_path):
        if filename.lower().endswith(('.pcap', '.pcapng')):
            pcap_files.append(os.path.join(folder_path, filename))

    if not pcap_files:
        print("No .pcap or .pcapng files found in the specified directory.")
        return

    for pcap_file in pcap_files:
        analyze_pcap(pcap_file)
        print("\n")

    print("[*] Scan complete.")


if __name__ == "__main__":
    main()