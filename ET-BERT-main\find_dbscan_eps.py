import argparse
import numpy as np
import pandas as pd
from sklearn.neighbors import NearestNeighbors
import matplotlib.pyplot as plt
import os

def main():
    parser = argparse.ArgumentParser(description="Find a good value for DBSCAN's eps parameter using a k-distance plot.")
    parser.add_argument("--feature_path", type=str, required=True, help="Path to the .npy file containing feature vectors.")
    parser.add_argument("--prediction_path", type=str, required=True, help="Path to the prediction file with binary labels.")
    parser.add_argument("--min_samples", type=int, default=5, help="The number of neighbors to consider for each point (same as min_samples for DBSCAN).")
    parser.add_argument("--unknown_label", type=int, default=1, help="The label representing the 'unknown' class in the prediction file.")
    parser.add_argument("--output_path", type=str, default="results/k-distance-plot.png", help="Path to save the generated plot.")

    args = parser.parse_args()

    # 1. Load data and filter for "unknown" class
    print("Loading data and filtering for 'unknown' class...")
    try:
        features = np.load(args.feature_path)
        predictions = pd.read_csv(args.prediction_path)
    except FileNotFoundError as e:
        print(f"Error loading file: {e}. Please check the paths.")
        return

    unknown_indices = predictions['label'] == args.unknown_label
    unknown_features = features[unknown_indices]

    if len(unknown_features) == 0:
        print("No samples were classified as 'unknown'. Exiting.")
        return

    print(f"Found {len(unknown_features)} 'unknown' samples to analyze.")

    # 2. Calculate k-distance
    print(f"Calculating distance to the {args.min_samples}th nearest neighbor for each point...")
    # n_neighbors is min_samples because we need to find the distance to the k-th neighbor (k=min_samples)
    # The point itself is included in the neighbors, so we need k+1 to get k neighbors if we were to exclude self, but sklearn handles this.
    # We ask for min_samples, and the distances array will have min_samples columns. The last column is the distance to the k-th neighbor.
    neighbors = NearestNeighbors(n_neighbors=args.min_samples, n_jobs=-1)
    neighbors_fit = neighbors.fit(unknown_features)
    distances, indices = neighbors_fit.kneighbors(unknown_features)

    # Sort the distances to the k-th neighbor
    # The distance to the k-th neighbor is the last column of the distances array
    k_distances = np.sort(distances[:, -1], axis=0)

    # 3. Plot the k-distance graph
    print("Generating k-distance plot...")
    plt.figure(figsize=(10, 6))
    plt.plot(k_distances)
    plt.xlabel("Points (sorted by distance)")
    plt.ylabel(f"Distance to {args.min_samples}th nearest neighbor (eps)")
    plt.title("K-Distance Graph for DBSCAN 'eps' Parameter")
    plt.grid(True)
    
    # Ensure the output directory exists
    output_dir = os.path.dirname(args.output_path)
    if not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)

    plt.savefig(args.output_path)
    print(f"Plot saved to {args.output_path}")
    print("\nLook for the 'elbow' or 'knee' in the plot. The y-value at this point is a good candidate for the 'eps' parameter.")


if __name__ == "__main__":
    main() 