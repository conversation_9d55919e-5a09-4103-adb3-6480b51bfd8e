import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import umap
import ast
import argparse
import os

def visualize_clusters(input_path, output_path=None, umap_n_neighbors=15, umap_min_dist=0.1, highlight_cluster=None, color_by='cluster'):
    """
    可视化HDBSCAN聚类结果。
    将高维嵌入再次通过UMAP降维到2维，并绘制散点图。
    """
    print(f"Loading data from {input_path}...")
    df = pd.read_csv(input_path, sep='\t')

    # 提取嵌入和聚类标签
    # 确保 'embedding' 列是字符串形式的列表或numpy数组表示，需要进行literal_eval
    # 如果是list，用pd.json_normalize(df['embedding']) 可能会更健壮，但这里假设是简单的列表字符串
    df['embedding'] = df['embedding'].apply(ast.literal_eval)
    embeddings = np.array(df['embedding'].tolist())
    cluster_labels = df['cluster_label'].values

    print(f"Loaded {embeddings.shape[0]} samples with {embeddings.shape[1]} dimensions.")
    print(f"Unique cluster labels: {np.unique(cluster_labels)}")

    # 对嵌入进行二次UMAP降维到2维
    print(f"Performing UMAP dimensionality reduction from {embeddings.shape[1]} to 2 dimensions for visualization...")
    reducer = umap.UMAP(n_components=2, n_neighbors=umap_n_neighbors, min_dist=umap_min_dist, random_state=42)
    embedding_2d = reducer.fit_transform(embeddings)
    print(f"UMAP reduction complete. New data shape: {embedding_2d.shape}")

    # 将2D嵌入和标签添加到DataFrame中，方便绘制
    df['umap_x'] = embedding_2d[:, 0]
    df['umap_y'] = embedding_2d[:, 1]
    df['cluster'] = cluster_labels.astype(str) # 将标签转为字符串，包括-1，方便seaborn处理

    # 打印聚类统计信息
    cluster_counts = df['cluster'].value_counts()
    print("\n聚类统计信息:")
    print("=" * 40)
    for cluster_id, count in cluster_counts.items():
        if cluster_id == '-1':
            print(f"噪声点: {count} 个样本")
        else:
            print(f"聚类 {cluster_id}: {count} 个样本")

    # 绘制散点图
    plt.figure(figsize=(15, 12))

    if color_by == 'app' and 'original_ground_truth_name' in df.columns:
        # 按应用类别着色
        print(f"\n按应用类别着色...")
        unique_apps = df['original_ground_truth_name'].unique()
        print(f"发现 {len(unique_apps)} 个不同的应用类别")

        # 使用更多颜色的调色板
        colors = plt.cm.tab20(np.linspace(0, 1, len(unique_apps)))

        for i, app_name in enumerate(sorted(unique_apps)):
            app_data = df[df['original_ground_truth_name'] == app_name]
            plt.scatter(
                app_data['umap_x'], app_data['umap_y'],
                c=[colors[i]],
                label=f'{app_name} ({len(app_data)}个)',
                alpha=0.7,
                s=30,
                edgecolors='black',
                linewidth=0.3
            )

        plt.title('聚类可视化 - 按应用类别着色 (UMAP 2D)')

    else:
        # 按聚类标签着色（原来的逻辑）
        print(f"\n按聚类标签着色...")
        noise_df = df[df['cluster'] == '-1']
        clustered_df = df[df['cluster'] != '-1']

        # 首先绘制聚类点
        if not clustered_df.empty:
            unique_clusters = sorted(clustered_df['cluster'].unique(), key=int)
            colors = plt.cm.Set3(np.linspace(0, 1, len(unique_clusters)))

            for i, cluster_id in enumerate(unique_clusters):
                cluster_data = clustered_df[clustered_df['cluster'] == cluster_id]

                # 如果指定了要高亮的聚类，给它特殊处理
                if highlight_cluster and cluster_id == str(highlight_cluster):
                    plt.scatter(
                        cluster_data['umap_x'], cluster_data['umap_y'],
                        c='red',
                        label=f'聚类 {cluster_id} ({len(cluster_data)}个) ⭐',
                        alpha=0.9,
                        s=50,
                        edgecolors='darkred',
                        linewidth=2
                    )
                else:
                    plt.scatter(
                        cluster_data['umap_x'], cluster_data['umap_y'],
                        c=[colors[i]],
                        label=f'聚类 {cluster_id} ({len(cluster_data)}个)',
                        alpha=0.7,
                        s=30,
                        edgecolors='black',
                        linewidth=0.5
                    )

        # 绘制噪音点
        if not noise_df.empty:
            plt.scatter(
                noise_df['umap_x'], noise_df['umap_y'],
                c='gray',
                marker='x',
                alpha=0.5,
                s=25,
                label=f'噪声 ({len(noise_df)}个)'
            )

        plt.title('聚类可视化 - 按聚类标签着色 (UMAP 2D)')
    
    plt.xlabel('UMAP Dimension 1')
    plt.ylabel('UMAP Dimension 2')
    plt.grid(True, linestyle='--', alpha=0.6)

    # 调整图例位置，防止遮挡
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left', borderaxespad=0., fontsize=8)
    plt.tight_layout()

    if output_path:
        plt.savefig(output_path, bbox_inches='tight', dpi=300)
        print(f"Plot saved to {output_path}")
    else:
        plt.show()

    # 分析每个聚类的应用分布
    print("\n聚类中的应用分布:")
    print("=" * 50)
    if 'original_ground_truth_name' in df.columns:
        for cluster_id in sorted(clustered_df['cluster'].unique(), key=int):
            cluster_data = df[df['cluster'] == cluster_id]
            app_counts = cluster_data['original_ground_truth_name'].value_counts()
            print(f"\n聚类 {cluster_id} ({len(cluster_data)}个样本):")
            for app, count in app_counts.head(10).items():  # 显示前10个应用
                percentage = (count / len(cluster_data)) * 100
                print(f"  {app}: {count} ({percentage:.1f}%)")

    return df

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Visualize HDBSCAN clustering results.')
    parser.add_argument('--input_path', type=str, required=True,
                        help='Path to the TSV file containing UMAP embeddings and cluster labels.')
    parser.add_argument('--output_path', type=str, default=None,
                        help='Optional path to save the generated plot (e.g., clusters.png). If not provided, the plot will be displayed.')
    parser.add_argument('--umap_n_neighbors', type=int, default=15,
                        help='The size of local neighborhood (in terms of number of neighboring sample points) used for manifold approximation. Defaults to 15.')
    parser.add_argument('--umap_min_dist', type=float, default=0.1,
                        help='The effective minimum distance between embedded points. Smaller values result in more clustered/clumped embeddings. Defaults to 0.1.')
    parser.add_argument('--highlight_cluster', type=int, default=None,
                        help='Highlight a specific cluster with red color and larger points.')
    parser.add_argument('--color_by', choices=['cluster', 'app'], default='cluster',
                        help='Color points by cluster labels or application categories.')

    args = parser.parse_args()

    # 检查输入文件是否存在
    if not os.path.exists(args.input_path):
        print(f"Error: Input file not found at {args.input_path}")
    else:
        visualize_clusters(args.input_path, args.output_path, args.umap_n_neighbors, args.umap_min_dist, args.highlight_cluster, args.color_by)