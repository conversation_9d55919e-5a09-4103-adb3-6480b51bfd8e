import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import umap
import ast
import argparse
import os

def visualize_clusters(input_path, output_path=None, umap_n_neighbors=15, umap_min_dist=0.1):
    """
    可视化HDBSCAN聚类结果。
    将高维嵌入再次通过UMAP降维到2维，并绘制散点图。
    """
    print(f"Loading data from {input_path}...")
    df = pd.read_csv(input_path, sep='\t')

    # 提取嵌入和聚类标签
    # 确保 'embedding' 列是字符串形式的列表或numpy数组表示，需要进行literal_eval
    # 如果是list，用pd.json_normalize(df['embedding']) 可能会更健壮，但这里假设是简单的列表字符串
    df['embedding'] = df['embedding'].apply(ast.literal_eval)
    embeddings = np.array(df['embedding'].tolist())
    cluster_labels = df['cluster_label'].values

    print(f"Loaded {embeddings.shape[0]} samples with {embeddings.shape[1]} dimensions.")
    print(f"Unique cluster labels: {np.unique(cluster_labels)}")

    # 对嵌入进行二次UMAP降维到2维
    print(f"Performing UMAP dimensionality reduction from {embeddings.shape[1]} to 2 dimensions for visualization...")
    reducer = umap.UMAP(n_components=2, n_neighbors=umap_n_neighbors, min_dist=umap_min_dist, random_state=42)
    embedding_2d = reducer.fit_transform(embeddings)
    print(f"UMAP reduction complete. New data shape: {embedding_2d.shape}")

    # 将2D嵌入和标签添加到DataFrame中，方便绘制
    df['umap_x'] = embedding_2d[:, 0]
    df['umap_y'] = embedding_2d[:, 1]
    df['cluster'] = cluster_labels.astype(str) # 将标签转为字符串，包括-1，方便seaborn处理

    # 绘制散点图
    plt.figure(figsize=(12, 10))
    
    # 区分噪音点和实际簇
    noise_df = df[df['cluster'] == '-1']
    clustered_df = df[df['cluster'] != '-1']

    # 首先绘制聚类点
    if not clustered_df.empty:
        sns.scatterplot(
            x='umap_x', y='umap_y',
            hue='cluster',
            palette=sns.color_palette("tab10", n_colors=len(clustered_df['cluster'].unique())), # 使用tab10或其他颜色板
            data=clustered_df,
            legend="full",
            alpha=0.8,
            s=20 # 点的大小
        )

    # 然后绘制噪音点，通常用不同的颜色和标记
    if not noise_df.empty:
        sns.scatterplot(
            x='umap_x', y='umap_y',
            data=noise_df,
            color='gray', # 灰色表示噪音
            marker='x', # 'x'标记表示噪音
            alpha=0.5,
            s=20, # 点的大小
            label='Noise' # 给噪音点加图例
        )
    
    plt.title('HDBSCAN Clustering Visualization (UMAP 2D)')
    plt.xlabel('UMAP Dimension 1')
    plt.ylabel('UMAP Dimension 2')
    plt.grid(True, linestyle='--', alpha=0.6)
    
    # 调整图例位置，防止遮挡
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left', borderaxespad=0.)
    plt.tight_layout()

    if output_path:
        plt.savefig(output_path, bbox_inches='tight')
        print(f"Plot saved to {output_path}")
    else:
        plt.show()

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Visualize HDBSCAN clustering results.')
    parser.add_argument('--input_path', type=str, required=True,
                        help='Path to the TSV file containing UMAP embeddings and cluster labels.')
    parser.add_argument('--output_path', type=str, default=None,
                        help='Optional path to save the generated plot (e.g., clusters.png). If not provided, the plot will be displayed.')
    parser.add_argument('--umap_n_neighbors', type=int, default=15,
                        help='The size of local neighborhood (in terms of number of neighboring sample points) used for manifold approximation. Defaults to 15.')
    parser.add_argument('--umap_min_dist', type=float, default=0.1,
                        help='The effective minimum distance between embedded points. Smaller values result in more clustered/clumped embeddings. Defaults to 0.1.')
    
    args = parser.parse_args()

    # 检查输入文件是否存在
    if not os.path.exists(args.input_path):
        print(f"Error: Input file not found at {args.input_path}")
    else:
        visualize_clusters(args.input_path, args.output_path, args.umap_n_neighbors, args.umap_min_dist) 