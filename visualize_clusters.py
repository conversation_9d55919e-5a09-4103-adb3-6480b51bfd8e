import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import umap
import ast
import argparse
import os
import random

def set_seed(seed=42):
    """设置所有随机种子以确保结果可重现"""
    random.seed(seed)
    np.random.seed(seed)
    print(f"可视化随机种子已设置为: {seed}")

def visualize_clusters(input_path, output_path=None, umap_n_neighbors=15, umap_min_dist=0.1, highlight_cluster=None):
    """
    可视化HDBSCAN聚类结果。
    将高维嵌入再次通过UMAP降维到2维，并绘制散点图。
    """
    # 设置随机种子
    set_seed(42)
    print(f"Loading data from {input_path}...")
    df = pd.read_csv(input_path, sep='\t')

    # 提取嵌入和聚类标签
    # 确保 'embedding' 列是字符串形式的列表或numpy数组表示，需要进行literal_eval
    # 如果是list，用pd.json_normalize(df['embedding']) 可能会更健壮，但这里假设是简单的列表字符串
    df['embedding'] = df['embedding'].apply(ast.literal_eval)
    embeddings = np.array(df['embedding'].tolist())
    cluster_labels = df['cluster_label'].values

    print(f"Loaded {embeddings.shape[0]} samples with {embeddings.shape[1]} dimensions.")
    print(f"Unique cluster labels: {np.unique(cluster_labels)}")

    # 对嵌入进行二次UMAP降维到2维
    print(f"Performing UMAP dimensionality reduction from {embeddings.shape[1]} to 2 dimensions for visualization...")
    reducer = umap.UMAP(n_components=2, n_neighbors=umap_n_neighbors, min_dist=umap_min_dist, random_state=42)
    embedding_2d = reducer.fit_transform(embeddings)
    print(f"UMAP reduction complete. New data shape: {embedding_2d.shape}")

    # 将2D嵌入和标签添加到DataFrame中，方便绘制
    df['umap_x'] = embedding_2d[:, 0]
    df['umap_y'] = embedding_2d[:, 1]
    df['cluster'] = cluster_labels.astype(str) # 将标签转为字符串，包括-1，方便seaborn处理

    # 打印聚类统计信息
    cluster_counts = df['cluster'].value_counts()
    print("\n聚类统计信息:")
    print("=" * 40)
    for cluster_id, count in cluster_counts.items():
        if cluster_id == '-1':
            print(f"噪声点: {count} 个样本")
        else:
            print(f"聚类 {cluster_id}: {count} 个样本")

    # 绘制散点图
    plt.figure(figsize=(15, 12))

    # 按应用类别着色，使用数字标识
    if 'original_ground_truth_name' in df.columns:
        print(f"\n按应用类别着色（使用数字标识）...")
        unique_apps = sorted(df['original_ground_truth_name'].unique())
        print(f"发现 {len(unique_apps)} 个不同的应用类别")

        # 创建应用名称到数字的映射
        app_to_num = {app: i for i, app in enumerate(unique_apps)}
        df['app_num'] = df['original_ground_truth_name'].map(app_to_num)

        # 使用更多颜色的调色板
        colors = plt.cm.tab20(np.linspace(0, 1, len(unique_apps)))

        for i, app_name in enumerate(unique_apps):
            app_data = df[df['original_ground_truth_name'] == app_name]
            app_num = app_to_num[app_name]

            # 如果指定了要高亮的聚类，检查该应用是否在高亮聚类中
            if highlight_cluster is not None:
                highlight_data = app_data[app_data['cluster'] == str(highlight_cluster)]
                normal_data = app_data[app_data['cluster'] != str(highlight_cluster)]

                # 绘制高亮部分
                if not highlight_data.empty:
                    plt.scatter(
                        highlight_data['umap_x'], highlight_data['umap_y'],
                        c=[colors[i]],
                        label=f'类别{app_num} ({len(app_data)}个) ⭐',
                        alpha=0.9,
                        s=50,
                        edgecolors='red',
                        linewidth=2
                    )

                # 绘制非高亮部分
                if not normal_data.empty:
                    plt.scatter(
                        normal_data['umap_x'], normal_data['umap_y'],
                        c=[colors[i]],
                        alpha=0.7,
                        s=30,
                        edgecolors='black',
                        linewidth=0.3
                    )
            else:
                plt.scatter(
                    app_data['umap_x'], app_data['umap_y'],
                    c=[colors[i]],
                    label=f'类别{app_num} ({len(app_data)}个)',
                    alpha=0.7,
                    s=30,
                    edgecolors='black',
                    linewidth=0.3
                )

        plt.title('聚类可视化 - 按应用类别着色 (UMAP 2D)')

        # 打印应用类别映射表
        print("\n应用类别映射表:")
        print("=" * 40)
        for app_name, num in app_to_num.items():
            count = len(df[df['original_ground_truth_name'] == app_name])
            print(f"类别{num}: {app_name} ({count}个样本)")

    else:
        print("错误：未找到 'original_ground_truth_name' 列")
    
    plt.xlabel('UMAP Dimension 1')
    plt.ylabel('UMAP Dimension 2')
    plt.grid(True, linestyle='--', alpha=0.6)

    # 调整图例位置，防止遮挡
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left', borderaxespad=0., fontsize=8)
    plt.tight_layout()

    if output_path:
        plt.savefig(output_path, bbox_inches='tight', dpi=300)
        print(f"Plot saved to {output_path}")
    else:
        plt.show()

    # 分析每个聚类的应用分布
    print("\n聚类中的应用分布:")
    print("=" * 50)
    if 'original_ground_truth_name' in df.columns:
        clustered_df = df[df['cluster'] != '-1']
        for cluster_id in sorted(clustered_df['cluster'].unique(), key=int):
            cluster_data = df[df['cluster'] == cluster_id]
            app_counts = cluster_data['original_ground_truth_name'].value_counts()
            print(f"\n聚类 {cluster_id} ({len(cluster_data)}个样本):")
            for app, count in app_counts.head(10).items():  # 显示前10个应用
                percentage = (count / len(cluster_data)) * 100
                app_num = app_to_num[app] if app in app_to_num else "未知"
                print(f"  类别{app_num} ({app}): {count} ({percentage:.1f}%)")

    return df

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Visualize HDBSCAN clustering results.')
    parser.add_argument('--input_path', type=str, required=True,
                        help='Path to the TSV file containing UMAP embeddings and cluster labels.')
    parser.add_argument('--output_path', type=str, default=None,
                        help='Optional path to save the generated plot (e.g., clusters.png). If not provided, the plot will be displayed.')
    parser.add_argument('--umap_n_neighbors', type=int, default=15,
                        help='The size of local neighborhood (in terms of number of neighboring sample points) used for manifold approximation. Defaults to 15.')
    parser.add_argument('--umap_min_dist', type=float, default=0.1,
                        help='The effective minimum distance between embedded points. Smaller values result in more clustered/clumped embeddings. Defaults to 0.1.')
    parser.add_argument('--highlight_cluster', type=int, default=None,
                        help='Highlight a specific cluster with red color and larger points.')

    args = parser.parse_args()

    # 检查输入文件是否存在
    if not os.path.exists(args.input_path):
        print(f"Error: Input file not found at {args.input_path}")
    else:
        visualize_clusters(args.input_path, args.output_path, args.umap_n_neighbors, args.umap_min_dist, args.highlight_cluster)