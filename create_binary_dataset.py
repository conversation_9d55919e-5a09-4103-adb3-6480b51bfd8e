#!/usr/bin/python3
# -*- coding:utf-8 -*-

import os
import sys
import random
import shutil
import subprocess
import csv
from multiprocessing import Pool, cpu_count
from tqdm import tqdm
from collections import Counter

# =================================================================================
# Configuration Section
# =================================================================================

# --- Core Paths ---
# !!! IMPORTANT !!! Please verify these paths before running the script.
PATH_KNOWN_APPS_ROOT = "F:\\app_data_7.13_已知类"
PATH_UNKNOWN_APPS_ROOT = "F:\\已知未知混合"
TSHARK_EXE_PATH = "C:\\Program Files\\Wireshark\\tshark.exe"

# --- Output Paths ---
WORKSPACE_ROOT = os.path.dirname(os.path.abspath(__file__))
DATASET_SAVE_DIR = os.path.join(WORKSPACE_ROOT, "datasets", "my_sni_only_classification")

# --- Dataset Parameters ---
TRAIN_SPLIT_RATIO = 0.7
VALID_TEST_SPLIT_RATIO = 0.5  # 0.5 of the remaining 30% is 15% of the total
RANDOM_SEED = 42

# --- Constants ---
LABEL_KNOWN = 0
LABEL_UNKNOWN = 1

# =================================================================================
# Helper & Core Logic Functions
# =================================================================================

def setup_directories():
    """Creates necessary output directories if they don't exist."""
    os.makedirs(DATASET_SAVE_DIR, exist_ok=True)
    print("="*50)
    print(f"SNI-only dataset will be saved in: {DATASET_SAVE_DIR}")
    print("="*50)

def tokenize_sni(sni_string):
    """
    Tokenizes an SNI string by converting it to a space-separated sequence of hex bytes.
    e.g., 'cat' -> '63 61 74'.
    This aligns the input format with the model's hexadecimal vocabulary.
    """
    if not sni_string:
        return ""
    # Convert string to hex representation, e.g., 'cat' -> b'cat' -> '636174'
    hex_representation = sni_string.encode('utf-8').hex()
    # Add spaces between bytes, e.g., '636174' -> '63 61 74'
    return " ".join(hex_representation[i:i+2] for i in range(0, len(hex_representation), 2))

def worker_get_sni_from_fields(task):
    """
    Worker function to process a single pcap file using tshark.
    It directly extracts the Server Name Indication (SNI) from TLS Client Hello packets.
    """
    pcap_path, label_id, app_name = task
    
    # Each pcap file will yield a set of unique SNIs found within it.
    # Using a set avoids duplicate SNIs from the same pcap.
    found_snis = set()

    try:
        # The tshark command to extract only the SNI field.
        # It's very fast and efficient.
        cmd = [
            TSHARK_EXE_PATH, "-r", str(pcap_path), "-n",
            "-Y", "tls.handshake.type == 1 && tls.handshake.extensions_server_name",
            "-T", "fields",
            "-e", "tls.handshake.extensions_server_name"
        ]
        
        # We run the command and capture its output directly.
        # stderr is captured to diagnose issues.
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore')

        if result.stderr and not result.stdout:
            # tqdm.write is thread-safe for printing from workers
                tqdm.write(f"  -> TSHARK WARNING for {pcap_path}: tshark produced no output. Stderr: {result.stderr.strip()}")

        # Process the output lines
        for line in result.stdout.strip().splitlines():
            sni = line.strip()
            # Some SNIs might be comma-separated if a packet has multiple, though rare.
            # We handle this by splitting and adding all of them.
            for single_sni in sni.split(','):
                if single_sni:
                    found_snis.add(single_sni)

        # Format the found SNIs into the desired output structure
        final_features = []
        for sni in found_snis:
            # Each unique SNI from the pcap becomes a data point
            tokenized_sni = tokenize_sni(sni)
            final_features.append((label_id, tokenized_sni, app_name, sni)) # Also return raw sni for truth file
        
        return (pcap_path, final_features)
    
    except Exception as e:
        tqdm.write(f"  -> An unexpected exception in worker for {pcap_path}: {e}")
        return (pcap_path, [])


# =================================================================================
# Main Data Preparation Logic (Largely unchanged, but simplified for SNI)
# =================================================================================

def main():
    """Main function to drive the dataset creation process."""
    setup_directories()
    random.seed(RANDOM_SEED)

    # --- Step 2: Discover the 'whitelist' of known applications ---
    known_app_whitelist = [d for d in os.listdir(PATH_KNOWN_APPS_ROOT) if os.path.isdir(os.path.join(PATH_KNOWN_APPS_ROOT, d))]
    print(f"Found {len(known_app_whitelist)} known applications (whitelist): {known_app_whitelist}\n")

    # --- Step 3: Collect all PCAPs from all sources ---
    all_known_pcaps = []
    all_unknown_pcaps = []
    pretender_known_pcaps = []

    print("Scanning primary KNOWN source...")
    for app_name in tqdm(known_app_whitelist, desc="Collecting Known Apps"):
        app_dir = os.path.join(PATH_KNOWN_APPS_ROOT, app_name)
        for pcap_file in os.listdir(app_dir):
            if pcap_file.lower().endswith(('.pcap', '.pcapng')):
                all_known_pcaps.append((os.path.join(app_dir, pcap_file), LABEL_KNOWN, app_name))
    
    print("\nScanning UNKNOWN/TEST source directory...")
    test_app_folders = [d for d in os.listdir(PATH_UNKNOWN_APPS_ROOT) if os.path.isdir(os.path.join(PATH_UNKNOWN_APPS_ROOT, d))]
    for app_name in tqdm(test_app_folders, desc="Collecting from Test Dir"):
        app_dir = os.path.join(PATH_UNKNOWN_APPS_ROOT, app_name)
        if app_name.endswith("_未知"):
            for pcap_file in os.listdir(app_dir):
                if pcap_file.lower().endswith(('.pcap', '.pcapng')):
                    all_unknown_pcaps.append((os.path.join(app_dir, pcap_file), LABEL_UNKNOWN, app_name))
        else:
            for pcap_file in os.listdir(app_dir):
                 if pcap_file.lower().endswith(('.pcap', '.pcapng')):
                    pretender_known_pcaps.append((os.path.join(app_dir, pcap_file), LABEL_KNOWN, app_name))

    # --- Step 4: Split pcaps into Train, Valid, Test sets using Stratified Logic ---
    print("\nSplitting datasets using stratified logic...")
    
    train_set_pcaps = []
    valid_set_pcaps = []
    test_set_pcaps = []

    all_pcaps_grouped = {}
    for pcap_path, label, app_name in all_known_pcaps + all_unknown_pcaps:
        if app_name not in all_pcaps_grouped:
            all_pcaps_grouped[app_name] = []
        all_pcaps_grouped[app_name].append((pcap_path, label, app_name))

    for app_name, pcap_list in all_pcaps_grouped.items():
        random.shuffle(pcap_list)
        train_idx = int(len(pcap_list) * TRAIN_SPLIT_RATIO)
        train_set_pcaps.extend(pcap_list[:train_idx])
        remaining = pcap_list[train_idx:]
        valid_idx = int(len(remaining) * VALID_TEST_SPLIT_RATIO)
        valid_set_pcaps.extend(remaining[:valid_idx])
        test_set_pcaps.extend(remaining[valid_idx:])

    test_set_pcaps.extend(pretender_known_pcaps)
    
    random.shuffle(train_set_pcaps)
    random.shuffle(valid_set_pcaps)
    random.shuffle(test_set_pcaps)

    print("\n--- Dataset Split Summary (Stratified Logic) ---")
    print(f"Total pcaps for Training:   {len(train_set_pcaps)}")
    print(f"Total pcaps for Validation: {len(valid_set_pcaps)}")
    print(f"Total pcaps for Testing:    {len(test_set_pcaps)}")
    print("--------------------------------------------------\n")

    # --- Step 5: Process PCAPs and write to TSV files ---
    num_processes = max(1, cpu_count() - 2)
    print(f"Starting SNI extraction with {num_processes} processes...")

    all_datasets = {"train": train_set_pcaps, "valid": valid_set_pcaps, "test": test_set_pcaps}
    test_ground_truth_data = []
    pcap_diagnostics = {
        app_name: {'processed_successfully': set()} for app_name in all_pcaps_grouped.keys()
    }
    for _, _, app_name in pretender_known_pcaps:
        if app_name not in pcap_diagnostics: pcap_diagnostics[app_name] = {'processed_successfully': set()}

    for name, pcap_list in all_datasets.items():
        output_path = os.path.join(DATASET_SAVE_DIR, f"{name}_dataset.tsv")
        with Pool(processes=num_processes) as pool, open(output_path, "w", encoding='utf-8') as f_out:
            f_out.write("label\ttext_a\n")
            tasks = [(pcap_path, label, app_name) for pcap_path, label, app_name in pcap_list]
            
            results_iterator = pool.imap_unordered(worker_get_sni_from_fields, tasks)
            
            for pcap_path_processed, sni_list in tqdm(results_iterator, total=len(tasks), desc=f"Processing {name.upper()} pcaps"):
                if sni_list:
                    app_name_from_result = sni_list[0][2]
                    if app_name_from_result in pcap_diagnostics:
                        pcap_diagnostics[app_name_from_result]['processed_successfully'].add(pcap_path_processed)
                
                for label, feature_string, app_name, raw_sni in sni_list:
                    f_out.write(f"{label}\t{feature_string}\n")
                    if name == "test":
                        test_ground_truth_data.append((label, feature_string, app_name, raw_sni))

    # --- Step 6: Write the ground truth file for the test set ---
    truth_output_path = os.path.join(DATASET_SAVE_DIR, "test_dataset_with_truth.tsv")
    print(f"\nWriting ground truth file for test set to: {truth_output_path}")
    with open(truth_output_path, "w", encoding='utf-8', newline='') as f_out:
        writer = csv.writer(f_out, delimiter='\t')
        writer.writerow(["label", "text_a", "ground_truth_name", "raw_sni"])
        for label, feature_string, app_name, raw_sni in test_ground_truth_data:
            writer.writerow([label, feature_string, app_name, raw_sni])

    print("\n\nDataset generation complete!")
    print(f"TSV files saved in: {DATASET_SAVE_DIR}")

    # --- Step 7: Print PCAP Processing Diagnostics ---
    print("\n--- PCAP Processing Diagnostics Report ---")
    all_source_pcaps = all_known_pcaps + all_unknown_pcaps + pretender_known_pcaps
    pcap_counts = Counter(item[2] for item in all_source_pcaps)
    print("Category".ljust(30), "Total PCAPs".ljust(15), "Processed PCAPs".ljust(20), "Success Rate (%)")
    print("-" * 90)
    total_scanned_overall = 0
    total_processed_overall = 0
    for app_name, total_count in sorted(pcap_counts.items()):
        processed_count = len(pcap_diagnostics.get(app_name, {}).get('processed_successfully', set()))
        total_scanned_overall += total_count
        total_processed_overall += processed_count
        rate = (processed_count / total_count * 100) if total_count > 0 else 0
        print(f"{app_name.ljust(30)} {str(total_count).ljust(15)} {str(processed_count).ljust(20)} {rate:.2f}")
    
    print("-" * 90)
    final_rate = (total_processed_overall / total_scanned_overall * 100) if total_scanned_overall > 0 else 0
    print(f"{'Overall'.ljust(30)} {str(total_scanned_overall).ljust(15)} {str(total_processed_overall).ljust(20)} {final_rate:.2f}")

if __name__ == '__main__':
    if not shutil.which(TSHARK_EXE_PATH) and not os.path.exists(TSHARK_EXE_PATH):
        print(f"Error: tshark.exe not found at the specified path: {TSHARK_EXE_PATH}")
        print("Please ensure Wireshark is installed and the path is correct, or tshark is in your system PATH.")
        sys.exit(1)
    
    main() 