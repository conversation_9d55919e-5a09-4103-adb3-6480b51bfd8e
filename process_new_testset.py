#!/usr/bin/python3
# -*- coding:utf-8 -*-

"""
This script is designed to process a new, separate directory of pcap files
and convert them into a single TSV file for model evaluation.

It applies the same SNI extraction and hex-encoding logic used in the main
dataset creation script, but without any data splitting. All processed data
is treated as a single test set.
"""

import os
import sys
import argparse
import subprocess
import csv
from multiprocessing import Pool, cpu_count
from tqdm import tqdm

# =================================================================================
# Reusable Core Logic (from create_binary_dataset.py)
# =================================================================================

TSHARK_EXE_PATH = "C:\\Program Files\\Wireshark\\tshark.exe"
LABEL_KNOWN = 0
LABEL_UNKNOWN = 1

def tokenize_sni(sni_string):
    """
    Tokenizes an SNI string by converting it to a space-separated sequence of hex bytes.
    """
    if not sni_string:
        return ""
    hex_representation = sni_string.encode('utf-8').hex()
    return " ".join(hex_representation[i:i+2] for i in range(0, len(hex_representation), 2))

def worker_get_sni_from_fields(task):
    """
    Worker function to extract SNI from a single pcap file.
    """
    pcap_path, app_name, label_id = task
    found_snis = set()
    try:
        cmd = [
            TSHARK_EXE_PATH, "-r", str(pcap_path), "-n",
            "-Y", "tls.handshake.type == 1 && tls.handshake.extensions_server_name",
            "-T", "fields",
            "-e", "tls.handshake.extensions_server_name"
        ]
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore')

        if result.stderr and not result.stdout:
            tqdm.write(f"  -> TSHARK WARNING for {pcap_path}: {result.stderr.strip()}")

        for line in result.stdout.strip().splitlines():
            sni = line.strip()
            for single_sni in sni.split(','):
                if single_sni:
                    found_snis.add(single_sni)

        final_features = []
        for sni in found_snis:
            # For a new test set, we might not know the true label.
            # We'll use a placeholder label (e.g., 0) as it won't be used
            # for accuracy calculation if we only care about predictions.
            # The ground_truth_name (app_name) is the most important part.
            final_features.append((label_id, tokenize_sni(sni), app_name, sni))
        
        return final_features
    
    except Exception as e:
        tqdm.write(f"  -> An unexpected exception in worker for {pcap_path}: {e}")
        return []

# =================================================================================
# Main Script Logic
# =================================================================================

def main():
    parser = argparse.ArgumentParser(
        description="Process a directory of new pcap files into a TSV test set.",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument("--input_dir", type=str, required=True,
                        help="Path to the root directory of the new dataset. This script will recursively find all .pcap/.pcapng files.")
    parser.add_argument("--output_file", type=str, required=True,
                        help="Path to save the output .tsv file.")
    
    args = parser.parse_args()

    print("="*50)
    print(f"Input data directory: {args.input_dir}")
    print(f"Output TSV file: {args.output_file}")
    print("="*50)

    # --- Step 1: Discover all PCAP files ---
    pcap_tasks = []
    print("Scanning for pcap files...")
    for root, _, files in os.walk(args.input_dir):
        for file in files:
            if file.lower().endswith(('.pcap', '.pcapng')):
                full_path = os.path.join(root, file)
                # We'll use the pcap's parent directory name as the "app_name"
                app_name = os.path.basename(root)
                # Assign the correct label based on the folder name
                if app_name.endswith("_未知"):
                    label_id = LABEL_UNKNOWN
                else:
                    label_id = LABEL_KNOWN
                pcap_tasks.append((full_path, app_name, label_id))
    
    if not pcap_tasks:
        print("No pcap files found. Exiting.")
        return
        
    print(f"Found {len(pcap_tasks)} pcap files to process.\n")

    # --- Step 2: Process PCAPs in parallel ---
    num_processes = max(1, cpu_count() - 2)
    print(f"Starting SNI extraction with {num_processes} processes...")
    
    all_results = []
    with Pool(processes=num_processes) as pool:
        results_iterator = pool.imap_unordered(worker_get_sni_from_fields, pcap_tasks)
        
        for sni_list in tqdm(results_iterator, total=len(pcap_tasks), desc="Processing pcaps"):
            if sni_list:
                all_results.extend(sni_list)

    # --- Step 3: Write results to TSV file ---
    print(f"\nWriting {len(all_results)} extracted SNIs to {args.output_file}")
    
    # Ensure the output directory exists
    os.makedirs(os.path.dirname(args.output_file), exist_ok=True)
    
    with open(args.output_file, "w", encoding='utf-8', newline='') as f_out:
        writer = csv.writer(f_out, delimiter='\t')
        # Write header consistent with test_dataset_with_truth.tsv
        writer.writerow(["label", "text_a", "ground_truth_name", "raw_sni"])
        for label, feature_string, app_name, raw_sni in all_results:
            writer.writerow([label, feature_string, app_name, raw_sni])

    print("\nProcessing complete!")
    print(f"TSV file saved successfully to: {args.output_file}")


if __name__ == '__main__':
    # Verify that tshark.exe exists before starting
    if not os.path.exists(TSHARK_EXE_PATH):
        print(f"Error: tshark.exe not found at the specified path: {TSHARK_EXE_PATH}")
        print("Please ensure Wireshark is installed and the path is correct.")
        sys.exit(1)
        
    main() 