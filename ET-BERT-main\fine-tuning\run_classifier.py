"""
This script provides an exmaple to wrap UER-py for classification.
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import random
import argparse
import torch
import torch.nn as nn
from uer.layers import *
from uer.encoders import *
from uer.utils.vocab import Vocab
from uer.utils.constants import *
from uer.utils import *
from uer.utils.optimizers import *
from uer.utils.config import load_hyperparam
from uer.utils.seed import set_seed
from uer.model_saver import save_model
from uer.opts import finetune_opts
import tqdm
import numpy as np
import functools
from multiprocessing import Pool, cpu_count
import functools
from torch.utils.data import Dataset, DataLoader, IterableDataset
import pandas as pd # For custom evaluation

class Classifier(nn.Module):
    def __init__(self, args, class_weights=None):
        super(Classifier, self).__init__()
        self.embedding = str2embedding[args.embedding](args, len(args.tokenizer.vocab))
        self.encoder = str2encoder[args.encoder](args)
        self.labels_num = args.labels_num
        self.pooling = args.pooling
        self.soft_targets = args.soft_targets
        self.soft_alpha = args.soft_alpha
        self.output_layer_1 = nn.Linear(args.hidden_size, args.hidden_size)
        self.output_layer_2 = nn.Linear(args.hidden_size, self.labels_num)
        self.class_weights = class_weights

    def forward(self, src, tgt, seg, soft_tgt=None):
        """
        Args:
            src: [batch_size x seq_length]
            tgt: [batch_size]
            seg: [batch_size x seq_length]
        """
        # Embedding.
        emb = self.embedding(src, seg)
        # Encoder.
        output = self.encoder(emb, seg)
        # Target.
        # Store pooled output before final classification layers for feature extraction
        pooled_output_for_feature_extraction = None 
        if self.pooling == "mean":
            pooled_output_for_feature_extraction = torch.mean(output, dim=1)
        elif self.pooling == "max":
            pooled_output_for_feature_extraction = torch.max(output, dim=1)[0]
        elif self.pooling == "last":
            pooled_output_for_feature_extraction = output[:, -1, :]
        else: # first
            pooled_output_for_feature_extraction = output[:, 0, :]
            
        output_for_classification = torch.tanh(self.output_layer_1(pooled_output_for_feature_extraction))
        logits = self.output_layer_2(output_for_classification)
        
        if tgt is not None: # Training or validation mode
            if self.soft_targets and soft_tgt is not None:
                loss = self.soft_alpha * nn.MSELoss()(logits, soft_tgt) + \
                       (1 - self.soft_alpha) * nn.NLLLoss(weight=self.class_weights)(nn.LogSoftmax(dim=-1)(logits), tgt.view(-1))
            else:
                loss = nn.NLLLoss(weight=self.class_weights)(nn.LogSoftmax(dim=-1)(logits), tgt.view(-1))
            return loss, logits
        else: # Inference mode (tgt is None)
            # In inference mode, return embeddings along with logits for feature extraction
            # 'output' here is the sequence of hidden states from the encoder, [batch_size, seq_length, hidden_size]
            # 'pooled_output_for_feature_extraction' is the pooled vector for classification [batch_size, hidden_size]
            return pooled_output_for_feature_extraction, logits, output # Return pooled, logits, and sequence output


def get_labels(path):
    """
    Get the set of unique labels from the dataset.
    """
    labels, columns = set(), {}
    with open(path, mode="r", encoding="utf-8") as f:
        for line_id, line in enumerate(f):
            if line_id == 0:
                for i, column_name in enumerate(line.strip().split("\t")):
                    columns[column_name] = i
                continue
            line = line.strip().split("\t")
            label = int(line[columns["label"]])
            labels.add(label)
    return sorted(list(labels))


def load_or_initialize_parameters(args, model):
    if args.pretrained_model_path is not None:
        # Initialize with pretrained model.
        # 1. Load the state dict from the checkpoint
        pretrained_state_dict = torch.load(args.pretrained_model_path, map_location={'cuda:1':'cuda:0', 'cuda:2':'cuda:0', 'cuda:3':'cuda:0'})
        
        # 2. Get the state dict of the current model
        model_state_dict = model.state_dict()
        
        # 3. Identify and remove weights from the pretrained model that have a shape mismatch with the current model.
        #    This is typically the word embedding layer when vocabularies differ.
        pretrained_state_dict_filtered = {}
        for k, v in pretrained_state_dict.items():
            if k in model_state_dict and model_state_dict[k].shape == v.shape:
                pretrained_state_dict_filtered[k] = v
            else:
                print(f"Skipping loading parameter '{k}' with shape {v.shape} due to shape mismatch with current model shape {model_state_dict.get(k, 'N/A')}.")

        # 4. Load the filtered state dict. `strict=False` is still a good idea
        #    in case there are other, non-critical differences.
        model.load_state_dict(pretrained_state_dict_filtered, strict=False)
        
    else:
        # Initialize with normal distribution.
        for n, p in list(model.named_parameters()):
            if "gamma" not in n and "beta" not in n:
                p.data.normal_(0, 0.02)


def build_optimizer(args, model):
    param_optimizer = list(model.named_parameters())
    no_decay = ['bias', 'gamma', 'beta']
    optimizer_grouped_parameters = [
                {'params': [p for n, p in param_optimizer if not any(nd in n for nd in no_decay)], 'weight_decay_rate': 0.01},
                {'params': [p for n, p in param_optimizer if any(nd in n for nd in no_decay)], 'weight_decay_rate': 0.0}
    ]
    if args.optimizer in ["adamw"]:
        optimizer = str2optimizer[args.optimizer](optimizer_grouped_parameters, lr=args.learning_rate, correct_bias=False)
    else:
        optimizer = str2optimizer[args.optimizer](optimizer_grouped_parameters, lr=args.learning_rate,
                                                  scale_parameter=False, relative_step=False)
    if args.scheduler in ["constant"]:
        scheduler = str2scheduler[args.scheduler](optimizer)
    elif args.scheduler in ["constant_with_warmup"]:
        scheduler = str2scheduler[args.scheduler](optimizer, args.train_steps*args.warmup)
    else:
        scheduler = str2scheduler[args.scheduler](optimizer, args.train_steps*args.warmup, args.train_steps)
    return optimizer, scheduler


def process_line(line, args, columns):
    """
    Worker function to process a single line from the dataset.
    """
    line_parts = line[:-1].split("\t")
    original_tgt = int(line_parts[columns["label"]])
    
    # Store original text_a, ground_truth_name, and raw_sni for later analysis
    original_text_a = line_parts[columns["text_a"]]
    original_ground_truth_name = line_parts[columns["ground_truth_name"]] if "ground_truth_name" in columns else "N/A"
    original_raw_sni = line_parts[columns["raw_sni"]] if "raw_sni" in columns else "N/A"

    # If the label was filtered out, return None to skip this sample.
    if original_tgt not in args.label_map:
        return None

    tgt = args.label_map[original_tgt]
    if args.soft_targets and "logits" in columns.keys():
        soft_tgt = [float(value) for value in line_parts[columns["logits"]].split(" ")]
    if "text_b" not in columns:  # Sentence classification.
        text_a = line_parts[columns["text_a"]]
        src = args.tokenizer.convert_tokens_to_ids([CLS_TOKEN] + args.tokenizer.tokenize(text_a))
        seg = [1] * len(src)
    else:  # Sentence-pair classification.
        text_a, text_b = line_parts[columns["text_a"]], line_parts[columns["text_b"]]
        src_a = args.tokenizer.convert_tokens_to_ids([CLS_TOKEN] + args.tokenizer.tokenize(text_a) + [SEP_TOKEN])
        src_b = args.tokenizer.convert_tokens_to_ids(args.tokenizer.tokenize(text_b) + [SEP_TOKEN])
        src = src_a + src_b
        seg = [1] * len(src_a) + [2] * len(src_b)

    if len(src) > args.seq_length:
        src = src[: args.seq_length]
        seg = seg[: args.seq_length]
    while len(src) < args.seq_length:
        src.append(0)
        seg.append(0)
    if args.soft_targets and "logits" in columns.keys():
        return (src, tgt, seg, soft_tgt, original_text_a, original_ground_truth_name, original_raw_sni)
    else:
        return (src, tgt, seg, original_text_a, original_ground_truth_name, original_raw_sni)


class LineByLineTextDataset(IterableDataset):
    """
    A memory-efficient dataset that reads and processes data line by line from a file.
    It tokenizes the text on-the-fly when requested by the DataLoader.
    """
    def __init__(self, args, path):
        self.args = args
        self.path = path
        self.start = 0
        self.end = -1
        
        # This count is for the progress bar and might not be perfectly accurate
        # if we are filtering out samples, but it's a good estimate.
        with open(path, "r", encoding="utf-8") as f:
            self.total_lines = sum(1 for _ in f) - 1 # Subtract 1 for header

    def __iter__(self):
        with open(self.path, "r", encoding="utf-8") as f:
            # Skip header
            header_line = f.readline()
            columns = {}
            for i, column_name in enumerate(header_line.strip().split("\t")):
                columns[column_name] = i

            for line in f:
                processed_line = process_line(line, self.args, columns)
                if processed_line is not None:
                    # Unpack depending on soft_targets, then repack with original data appended
                    if self.args.soft_targets and "logits" in columns.keys():
                        src, tgt, seg, soft_tgt, original_text_a, original_ground_truth_name, original_raw_sni = processed_line
                        yield (src, tgt, seg, soft_tgt, original_text_a, original_ground_truth_name, original_raw_sni)
                    else:
                        src, tgt, seg, original_text_a, original_ground_truth_name, original_raw_sni = processed_line
                        yield (src, tgt, seg, original_text_a, original_ground_truth_name, original_raw_sni)

def collate_fn(batch):
    """
    Custom collate function to batch processed samples together.
    `batch` is a list of tuples, where each tuple is the output of `process_line`.
    e.g., [(src1, tgt1, seg1, original_text_a1, ...), (src2, tgt2, seg2, original_text_a2, ...), ...]
    """
    src_list, tgt_list, seg_list = [], [], []
    soft_tgt_list = []
    original_text_a_list, original_ground_truth_name_list, original_raw_sni_list = [], [], []

    has_soft_target = False

    for sample in batch:
        # Determine the structure of the sample based on whether soft_targets are used
        if len(sample) == 7: # src, tgt, seg, soft_tgt, original_text_a, original_ground_truth_name, original_raw_sni
            src_list.append(sample[0])
            tgt_list.append(sample[1])
            seg_list.append(sample[2])
            soft_tgt_list.append(sample[3])
            original_text_a_list.append(sample[4])
            original_ground_truth_name_list.append(sample[5])
            original_raw_sni_list.append(sample[6])
            has_soft_target = True
        elif len(sample) == 6: # src, tgt, seg, original_text_a, original_ground_truth_name, original_raw_sni
            src_list.append(sample[0])
            tgt_list.append(sample[1])
            seg_list.append(sample[2])
            original_text_a_list.append(sample[3])
            original_ground_truth_name_list.append(sample[4])
            original_raw_sni_list.append(sample[5])
        else:
            # Fallback for unexpected sample structure (e.g., during training if soft_targets are not used)
            # This case should ideally not be hit in evaluation mode when we expect original data.
            src_list.append(sample[0])
            tgt_list.append(sample[1])
            seg_list.append(sample[2])
            if len(sample) > 3: # Handle soft_targets for backward compatibility
                soft_tgt_list.append(sample[3])
                has_soft_target = True
            original_text_a_list.append("N/A") # Placeholder
            original_ground_truth_name_list.append("N/A")
            original_raw_sni_list.append("N/A")

    src_batch = torch.LongTensor(src_list)
    tgt_batch = torch.LongTensor(tgt_list)
    seg_batch = torch.LongTensor(seg_list)
    
    return_tuple = (src_batch, tgt_batch, seg_batch)
    if has_soft_target: # This condition might need refinement if soft_targets are not universally present
        soft_tgt_batch = torch.FloatTensor(soft_tgt_list)
        return_tuple += (soft_tgt_batch,)

    # Append original data as the last elements of the returned tuple
    return_tuple += (original_text_a_list, original_ground_truth_name_list, original_raw_sni_list)

    return return_tuple


def read_dataset(args, path, Pool, tqdm, functools, cpu_count):
    # This function is now deprecated and will be replaced by the IterableDataset.
    # Kept here to avoid breaking other parts of the code if they still call it,
    # but it should not be used for the main training loop.
    print("Warning: `read_dataset` is deprecated.")
    return []


def train_model(args, model, optimizer, scheduler, src_batch, tgt_batch, seg_batch, soft_tgt_batch=None):
    model.zero_grad()

    src_batch = src_batch.to(args.device)
    tgt_batch = tgt_batch.to(args.device)
    seg_batch = seg_batch.to(args.device)
    if soft_tgt_batch is not None:
        soft_tgt_batch = soft_tgt_batch.to(args.device)

    loss, _ = model(src_batch, tgt_batch, seg_batch, soft_tgt_batch)
    if torch.cuda.device_count() > 1:
        loss = torch.mean(loss)

    if args.fp16:
        with args.amp.scale_loss(loss, optimizer) as scaled_loss:
            scaled_loss.backward()
    else:
        loss.backward()

    torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)

    optimizer.step()
    scheduler.step()

    return loss


def evaluate(args, model, dataset, print_confusion_matrix=False):
    # The 'dataset' for evaluation is now an IterableDataset, so we use a DataLoader.
    # We can't easily get the total length, so we will adapt the logic.
    
    eval_dataloader = DataLoader(dataset, batch_size=args.batch_size, collate_fn=collate_fn)
    
    correct = 0
    total = 0
    total_loss = 0
    # Confusion matrix.
    # The size of confusion matrix should be based on the number of labels we are actually using.
    confusion = torch.zeros(args.labels_num, args.labels_num, dtype=torch.long)

    model.eval()

    with torch.no_grad():
        for i, (src_batch, tgt_batch, seg_batch, soft_tgt_batch) in enumerate(eval_dataloader):
            # Skip batches that are empty after filtering
            if src_batch is None or src_batch.size(0) == 0:
                continue

            src_batch = src_batch.to(args.device)
            tgt_batch = tgt_batch.to(args.device)
            seg_batch = seg_batch.to(args.device)

            loss, logits = model(src_batch, tgt_batch, seg_batch)
            if torch.cuda.device_count() > 1:
                loss = torch.mean(loss)
            total_loss += loss.item() * src_batch.size(0)
            
            pred = torch.argmax(logits, dim=1)
            gold = tgt_batch
            
            for j in range(pred.size()[0]):
                confusion[pred[j], gold[j]] += 1

            correct_preds = pred.eq(gold.view_as(pred)).sum()
            correct += correct_preds.item()
            total += src_batch.size(0)
    
    # Avoid division by zero if dev set is empty after filtering
    if total == 0:
        print("Warning: No samples found in the evaluation set after filtering. Skipping evaluation.")
        return 0.0, 0.0, confusion # acc, avg_loss, confusion

    acc = correct / total
    avg_loss = total_loss / total
    print("Acc. (Correct/Total): {:.4f} ({}/{}) ".format(acc, correct, total))
    print(f"Avg. Val Loss: {avg_loss:.4f}")
    
    if print_confusion_matrix:
        print("Confusion matrix:")
        print(confusion)
        # You can add more detailed metrics here if needed, like precision, recall, f1 per class
        # For example:
        # tp = confusion.diag()
        # for i in range(args.labels_num):
        #     p = tp[i] / confusion[:, i].sum().item() if confusion[:, i].sum().item() > 0 else 0
        #     r = tp[i] / confusion[i, :].sum().item() if confusion[i, :].sum().item() > 0 else 0
        #     f1 = 2 * p * r / (p + r) if (p + r) > 0 else 0
        #     print(f"Class {i}: Precision={p:.4f}, Recall={r:.4f}, F1={f1:.4f}")


    return acc, avg_loss, confusion


def main():
    parser = argparse.ArgumentParser(formatter_class=argparse.ArgumentDefaultsHelpFormatter)

    finetune_opts(parser)

    parser.add_argument("--pooling", choices=["mean", "max", "first", "last"], default="first",
                        help="Pooling type.")
    
    # --- Custom mode argument ---
    parser.add_argument("--mode", choices=["train", "eval"], default="train",
                        help="Set to 'eval' to run evaluation on the test set.")
    
    # --- Custom evaluation output argument ---
    parser.add_argument("--output_eval_features_path", type=str, default=None,
                        help="Path to save a TSV file containing evaluation results and extracted features (embeddings).")
    parser.add_argument("--extract_embedding_type", choices=["pooled", "sequence"], default="pooled",
                        help="Type of embedding to extract in eval mode: 'pooled' (default, for classification) or 'sequence' (for contextual embeddings).")
    parser.add_argument("--extract_layer", type=int, default=-1,
                        help="Which encoder layer's output to extract as embedding features (0 to num_layers-1, or -1 for the final layer). Only applicable if extract_embedding_type is 'sequence'.")

    parser.add_argument("--tokenizer", choices=["bert", "char", "space"], default="bert",
                        help="Specify the tokenizer."
                             "Original Google BERT uses bert tokenizer on Chinese corpus."
                             "Char tokenizer segments sentences into characters."
                             "Space tokenizer segments sentences into words according to space."
                        )

    parser.add_argument("--soft_targets", action="store_true",
                        help="Train model with soft targets.")
    parser.add_argument("--soft_alpha", type=float, default=0.5,
                        help="Weight of the soft targets loss.")
    parser.add_argument("--class_0_weight_factor", type=float, default=1.0,
                        help="A multiplier for the weight of class 0 to increase its importance.")

    args = parser.parse_args()
    
    # Imports moved inside main function to be compatible with Windows multiprocessing
    import torch
    import torch.nn as nn
    from multiprocessing import Pool, cpu_count
    import tqdm
    import functools

    # Load the hyperparameters from the config file.
    args = load_hyperparam(args)

    # Get label set and map labels to 0-indexed integers.
    labels = get_labels(args.train_path)
    
    # --- Start of new logic for handling class imbalance ---

    # Report original label distribution
    print("Original label distribution in training data:")
    label_counts = {label: 0 for label in labels}
    columns = {}
    with open(args.train_path, mode="r", encoding="utf-8") as f:
        for i, line in enumerate(f):
            if i == 0:
                for j, column_name in enumerate(line.strip().split("\t")):
                    columns[column_name] = j
                continue
            line = line.strip().split("\t")
            label = int(line[columns["label"]])
            if label in label_counts:
                label_counts[label] += 1
    
    total_samples = sum(label_counts.values())
    print(f"Total number of original labels: {len(labels)}")
    print(f"Total number of training samples: {total_samples}")
    for label, count in sorted(label_counts.items()):
        percentage = (count / total_samples) * 100
        print(f"  Label {label}: {count} samples ({percentage:.2f}%)")

    # Filter out rare classes
    MIN_SAMPLES_THRESHOLD = 10
    labels_to_keep = {l for l, c in label_counts.items() if c >= MIN_SAMPLES_THRESHOLD}
    
    if len(labels_to_keep) < len(labels):
        print(f"\nFiltering out classes with fewer than {MIN_SAMPLES_THRESHOLD} samples.")
        print(f"Kept {len(labels_to_keep)} out of {len(labels)} classes.")
    
    args.labels_num = len(labels_to_keep)
    # Create a new sorted list of labels for consistent mapping
    sorted_labels_to_keep = sorted(list(labels_to_keep))
    label_map = {label: i for i, label in enumerate(sorted_labels_to_keep)}
    args.label_map = label_map

    # Calculate class weights for the remaining classes
    total_samples_kept = sum(label_counts[l] for l in labels_to_keep)
    weights = []
    for label in sorted_labels_to_keep:
        weight = total_samples_kept / (args.labels_num * label_counts[label])
        if label == 0:
            weight *= args.class_0_weight_factor
        weights.append(weight)

    # --- End of new logic ---

    # Build tokenizer.
    args.tokenizer = str2tokenizer[args.tokenizer](args)

    # Build classification model.
    # Move device setup earlier to put weights on the correct device
    args.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
    class_weights = torch.FloatTensor(weights).to(args.device)
    print("\nApplied class weights to loss function:")
    for label, weight in zip(sorted_labels_to_keep, weights):
        print(f"  Label {label} (mapped to {label_map[label]}): weight={weight:.2f}")
        if label == 0 and args.class_0_weight_factor != 1.0:
            print(f"    (Applied a factor of {args.class_0_weight_factor})")

    model = Classifier(args, class_weights=class_weights)

    # Load or initialize parameters.
    load_or_initialize_parameters(args, model)
    model.to(args.device)

    # ====== EVALUATION MODE ======
    if args.mode == "eval":
        print("\n***** Running Evaluation Only *****")
        if not args.test_path:
            print("Error: --test_path is required for evaluation mode.")
            sys.exit(1)
        
        print(f"Loading test data from {args.test_path}...")
        # Modify LineByLineTextDataset to also return raw data
        test_dataset = LineByLineTextDataset(args, args.test_path)
        
        model.eval()
        
        eval_dataloader = DataLoader(test_dataset, batch_size=args.batch_size, collate_fn=collate_fn)
        
        all_predictions = []
        all_gold = []
        all_embeddings = [] # To store extracted embeddings
        all_original_text_a = []
        all_original_ground_truth_name = []
        all_original_raw_sni = []

        with torch.no_grad():
            for i, batch in enumerate(tqdm.tqdm(eval_dataloader, desc="Evaluating")):
                # Unpack the batch based on the expected structure from collate_fn
                # The last three elements are original_text_a, original_ground_truth_name, original_raw_sni
                if len(batch) == 7: # src, tgt, seg, soft_tgt, original_text_a, original_ground_truth_name, original_raw_sni
                    src_batch, tgt_batch, seg_batch, soft_tgt_batch, original_text_a_batch, original_ground_truth_name_batch, original_raw_sni_batch = batch
                else: # src, tgt, seg, original_text_a, original_ground_truth_name, original_raw_sni
                    src_batch, tgt_batch, seg_batch, original_text_a_batch, original_ground_truth_name_batch, original_raw_sni_batch = batch

                if src_batch.size(0) == 0: continue
                src_batch, seg_batch = src_batch.to(args.device), seg_batch.to(args.device)
                
                # Call model with tgt=None to get embeddings and logits for inference
                # Now model returns pooled_output, logits, and sequence_output
                pooled_embedding, logits, sequence_output = model(src_batch, None, seg_batch)
                
                all_predictions.extend(torch.argmax(logits, dim=1).cpu().numpy())
                all_gold.extend(tgt_batch.cpu().numpy())

                # Determine which embedding to extract
                if args.extract_embedding_type == "sequence":
                    # If extracting sequence embeddings, take the specified layer's output
                    # For simplicity, we'll take the [CLS] token's embedding from that layer if no specific token is needed.
                    # Otherwise, you might need a different pooling for sequence output.
                    # For clustering, we usually want a single vector per sample.
                    # So, let's take the [CLS] token's embedding (first token) from the specified layer.
                    # output is [batch_size, seq_length, hidden_size]
                    if args.extract_layer == -1: # Default to final layer
                        current_embeddings = sequence_output[:, 0, :].cpu().numpy() # [CLS] token of final layer
                    elif 0 <= args.extract_layer < sequence_output.size(1): # Check if layer index is valid
                        # This logic needs to be careful: sequence_output might be only the final layer in default UER-py setups.
                        # If UER-py's encoder returns all layer outputs, then `output` would be a list of tensors.
                        # Assuming `output` in Classifier.forward is indeed the final layer's sequence output:
                        current_embeddings = sequence_output[:, 0, :].cpu().numpy() # Still taking CLS from final layer
                        # To extract from *intermediate* layers, the encoder needs to return them. UER-py's default TransformerEncoder returns only the last layer's output.
                        # For true intermediate layers, a deeper change in uer/encoders/transformer_encoder.py would be needed.
                        # For now, we'll stick to pooled or the final sequence CLS token.
                        print("Warning: extract_layer only applies if encoder returns intermediate layers. Defaulting to CLS token from final layer sequence output.")
                        current_embeddings = sequence_output[:, 0, :].cpu().numpy()
                    else:
                        print("Error: Invalid extract_layer specified. Defaulting to pooled embedding.")
                        current_embeddings = pooled_embedding.cpu().numpy()
                else: # Default to 'pooled' embedding
                    current_embeddings = pooled_embedding.cpu().numpy()
                
                all_embeddings.extend(current_embeddings)

                all_original_text_a.extend(original_text_a_batch)
                all_original_ground_truth_name.extend(original_ground_truth_name_batch)
                all_original_raw_sni.extend(original_raw_sni_batch)

        print("\n--- Final Test Set Evaluation Results ---")
        
        gold_labels = torch.LongTensor(all_gold)
        pred_labels = torch.LongTensor(all_predictions)

        # Overall Accuracy
        correct_predictions = (pred_labels == gold_labels).sum().item()
        total_predictions = len(gold_labels)
        accuracy = correct_predictions / total_predictions
        print(f"Accuracy: {accuracy:.4f} ({correct_predictions}/{total_predictions})")

        # Confusion Matrix
        confusion = torch.zeros(args.labels_num, args.labels_num, dtype=torch.long)
        for i in range(total_predictions):
            confusion[pred_labels[i], gold_labels[i]] += 1
        
        print("\nConfusion Matrix:")
        print("         Predicted")
        print("         Known(0)  Unknown(1)")
        print("True")
        print(f"Known(0)   {confusion[0, 0]:<8}  {confusion[1, 0]:<8}")
        print(f"Unknown(1) {confusion[0, 1]:<8}  {confusion[1, 1]:<8}")

        # Detailed Metrics Calculation
        TP = confusion[0, 0].item()  # True Positives: correctly identified as Known
        FN = confusion[1, 0].item()  # False Negatives: Known but identified as Unknown (漏报)
        FP = confusion[0, 1].item()  # False Positives: Unknown but identified as Known (误报)
        TN = confusion[1, 1].item()  # True Negatives: correctly identified as Unknown

        # Leakage Rate (FNR)
        fnr = FN / (TP + FN) if (TP + FN) > 0 else 0
        print(f"\n漏报率 (False Negative Rate): {fnr:.4f} ({FN}/{TP+FN})")
        
        # False Discovery Rate (FDR)
        fdr = FP / (TN + FP) if (TN + FP) > 0 else 0
        print(f"误报率 (False Discovery Rate): {fdr:.4f} ({FP}/{TN+FP})")
        print("---------------------------------------")
        
        # --- Save detailed evaluation results and features if path is provided ---
        if args.output_eval_features_path:
            print(f"\nSaving detailed evaluation features to: {args.output_eval_features_path}")
            output_df = pd.DataFrame({
                'true_label': all_gold,
                'predicted_label': all_predictions,
                'embedding': [emb.tolist() for emb in all_embeddings], # Convert numpy array to list for TSV
                'original_text_a': all_original_text_a,
                'original_ground_truth_name': all_original_ground_truth_name,
                'original_raw_sni': all_original_raw_sni
            })
            
            # Ensure output directory exists
            os.makedirs(os.path.dirname(args.output_eval_features_path), exist_ok=True)
            output_df.to_csv(args.output_eval_features_path, sep='\t', index=False)
            print("Detailed features saved successfully.")

        sys.exit(0) # Exit after evaluation is complete

    # ====== TRAINING MODE (Original Logic) ======

    # Initialize the data loader for the training set.
    train_dataset = LineByLineTextDataset(args, args.train_path)
    
    # Adjust train_steps based on the number of samples we are actually keeping
    args.train_steps = (total_samples_kept // args.batch_size) * args.epochs_num
    
    train_dataloader = DataLoader(train_dataset, batch_size=args.batch_size, collate_fn=collate_fn)

    # Build optimizer.
    optimizer, scheduler = build_optimizer(args, model)

    if args.fp16:
        try:
            from apex import amp
        except ImportError:
            raise ImportError("Please install apex from https://www.github.com/nvidia/apex to use fp16 training.")
        model, optimizer = amp.initialize(model, optimizer, opt_level=args.fp16_opt_level)
        args.amp = amp

    if torch.cuda.device_count() > 1:
        print("{} GPUs are available. Let's use them.".format(torch.cuda.device_count()))
        model = nn.DataParallel(model)

    total_loss, total_correct, total_instances = 0., 0, 0
    best_loss = float('inf')  # Variable to track the best validation loss

    print("\n***** Running training *****")
    print(f"  Num examples = {total_samples_kept}")
    print(f"  Batch size = {args.batch_size}")
    print(f"  Num Epochs = {args.epochs_num}")

    steps_per_epoch = (total_samples_kept + args.batch_size - 1) // args.batch_size

    # --- Debugging: Add a flag to check weights only on the first step ---
    first_step_check = True

    for epoch in range(1, args.epochs_num + 1):
        model.train()
        # Create a new progress bar for each epoch.
        progress_bar = tqdm.tqdm(train_dataloader, desc=f"Epoch {epoch}", total=steps_per_epoch)
        
        for i, (src_batch, tgt_batch, seg_batch, soft_tgt_batch) in enumerate(progress_bar):
            
            loss = train_model(args, model, optimizer, scheduler, src_batch, tgt_batch, seg_batch, soft_tgt_batch)

            total_loss += loss.item()
            total_instances += src_batch.size(0)
            
            if (i + 1) % args.report_steps == 0:
                print("Epoch id: {}, Training loss: {:.4f}".format(epoch, total_loss / total_instances))
                total_loss = 0.
                total_instances = 0
            
            progress_bar.set_postfix(loss=loss.item())
        
        # --- Evaluation and Model Saving inside the loop ---
        print(f"--- Finished Epoch {epoch}, evaluating on dev set ---")
        dev_dataset = LineByLineTextDataset(args, args.dev_path)
        _, current_loss, _ = evaluate(args, model, dev_dataset, print_confusion_matrix=True)
        
        if current_loss < best_loss:
            best_loss = current_loss
            print(f"*** New best validation loss: {best_loss:.4f}. Saving model to {args.output_model_path} ***")
            save_model(model, args.output_model_path)
        else:
            print(f"Validation loss did not improve. Current best: {best_loss:.4f}")

    # The final model saved is the one with the best accuracy on the dev set.
    print(f"\nTraining complete. The best model (Loss: {best_loss:.4f}) is saved at {args.output_model_path}")


if __name__ == "__main__":
    main()
