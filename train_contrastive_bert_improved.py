#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的ET-BERT端到端对比学习
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'ET-BERT-main'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'ET-BERT-main', 'fine-tuning'))

import argparse
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pandas as pd
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import LabelEncoder
from collections import Counter
import random
from uer.layers import *
from uer.encoders import *
from uer.utils.vocab import Vocab
from uer.utils.constants import *
from uer.utils import *
from uer.utils.config import load_hyperparam
from uer.model_loader import load_model
from run_classifier import Classifier, load_or_initialize_parameters

def set_seed(seed=42):
    """设置所有随机种子以确保结果可重现"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    print(f"随机种子已设置为: {seed}")

class ImprovedContrastiveBERT(Classifier):
    """
    改进的对比学习模型
    """
    def __init__(self, args, projection_dim=64):
        super(ImprovedContrastiveBERT, self).__init__(args)
        
        # 更小的投影头，减少过拟合
        self.projection_head = nn.Sequential(
            nn.Linear(args.hidden_size, projection_dim * 2),
            nn.ReLU(inplace=True),
            nn.Dropout(0.1),
            nn.Linear(projection_dim * 2, projection_dim),
            nn.LayerNorm(projection_dim)
        )

    def forward(self, src, tgt, seg, return_features=False):
        # ET-BERT特征提取
        emb = self.embedding(src, seg)
        output = self.encoder(emb, seg)
        
        # 池化
        if self.pooling == "mean":
            pooled_output = torch.mean(output, dim=1)
        elif self.pooling == "max":
            pooled_output = torch.max(output, dim=1)[0]
        elif self.pooling == "last":
            pooled_output = output[:, -1, :]
        else:  # first (CLS token)
            pooled_output = output[:, 0, :]
        
        # 投影
        projected_features = self.projection_head(pooled_output)
        
        if return_features:
            return pooled_output, projected_features
        return projected_features

class InfoNCELoss(nn.Module):
    """
    InfoNCE损失函数 - 更稳定的对比学习损失
    """
    def __init__(self, temperature=0.1):
        super(InfoNCELoss, self).__init__()
        self.temperature = temperature

    def forward(self, features, labels):
        batch_size = features.shape[0]
        
        # 归一化特征
        features = nn.functional.normalize(features, dim=1)
        
        # 计算相似度矩阵
        similarity_matrix = torch.matmul(features, features.T) / self.temperature
        
        # 创建正样本mask
        labels = labels.contiguous().view(-1, 1)
        mask = torch.eq(labels, labels.T).float().to(features.device)
        
        # 移除对角线
        mask.fill_diagonal_(0)
        
        # 计算InfoNCE损失
        # 对于每个anchor，计算与所有正样本的相似度
        exp_sim = torch.exp(similarity_matrix)
        
        # 分母：与所有样本的相似度（除了自己）
        exp_sim_sum = exp_sim.sum(dim=1) - torch.diag(exp_sim)
        
        # 分子：与正样本的相似度
        pos_sim = (exp_sim * mask).sum(dim=1)
        
        # 避免除零
        pos_sim = torch.clamp(pos_sim, min=1e-8)
        exp_sim_sum = torch.clamp(exp_sim_sum, min=1e-8)
        
        # InfoNCE损失
        loss = -torch.log(pos_sim / exp_sim_sum)
        
        # 只计算有正样本的anchor的损失
        valid_mask = (mask.sum(dim=1) > 0)
        if valid_mask.sum() > 0:
            loss = loss[valid_mask].mean()
        else:
            loss = torch.tensor(0.0, device=features.device, requires_grad=True)
        
        return loss

def read_contrastive_dataset(args, texts, labels):
    """
    创建对比学习数据集
    """
    dataset = []
    for text, label in zip(texts, labels):
        # 使用ET-BERT的标准分词方式
        src = args.tokenizer.convert_tokens_to_ids([CLS_TOKEN] + args.tokenizer.tokenize(text) + [SEP_TOKEN])
        seg = [1] * len(src)
        
        # 截断或填充
        if len(src) > args.seq_length:
            src = src[:args.seq_length]
            seg = seg[:args.seq_length]
        while len(src) < args.seq_length:
            src.append(0)
            seg.append(0)
            
        dataset.append((src, seg, label))
    
    return dataset

def collate_fn_contrastive(batch):
    """
    对比学习的collate函数
    """
    src_batch = torch.LongTensor([item[0] for item in batch])
    seg_batch = torch.LongTensor([item[1] for item in batch])
    label_batch = torch.LongTensor([item[2] for item in batch])
    
    return src_batch, seg_batch, label_batch

def train_model(model, dataloader, criterion, optimizer, device, epochs):
    """
    训练对比学习模型
    """
    model.train()
    for epoch in range(epochs):
        total_loss = 0
        num_batches = 0
        
        for src_batch, seg_batch, labels_batch in dataloader:
            src_batch = src_batch.to(device)
            seg_batch = seg_batch.to(device)
            labels_batch = labels_batch.to(device)

            optimizer.zero_grad()
            
            # 前向传播
            projected_features = model(src_batch, labels_batch, seg_batch)
            
            # 计算损失
            loss = criterion(projected_features, labels_batch)
            
            # 检查损失是否有效
            if torch.isnan(loss) or torch.isinf(loss):
                print(f"Warning: Invalid loss detected: {loss}")
                continue
            
            # 反向传播
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
            
        avg_loss = total_loss / num_batches if num_batches > 0 else 0
        print(f"Epoch {epoch+1}/{epochs}, Average Loss: {avg_loss:.4f}")

def extract_features(model, dataloader, device):
    """
    提取特征
    """
    model.eval()
    all_bert_features = []
    all_projected_features = []
    
    with torch.no_grad():
        for src_batch, seg_batch, labels_batch in dataloader:
            src_batch = src_batch.to(device)
            seg_batch = seg_batch.to(device)
            labels_batch = labels_batch.to(device)
            
            bert_features, projected_features = model(src_batch, labels_batch, seg_batch, return_features=True)
            
            all_bert_features.append(bert_features.cpu().numpy())
            all_projected_features.append(projected_features.cpu().numpy())
    
    return np.concatenate(all_bert_features, axis=0), np.concatenate(all_projected_features, axis=0)

def main():
    # 首先设置随机种子
    set_seed(42)

    parser = argparse.ArgumentParser(description='改进的ET-BERT端到端对比学习')
    
    # 数据参数
    parser.add_argument('--input_features_path', type=str, 
                        default='results/evaluated_test_features_sequence.tsv')
    parser.add_argument('--pretrained_model_path', type=str,
                        default='ET-BERT-main/models/pre-trained_model.bin')
    parser.add_argument('--vocab_path', type=str,
                        default='ET-BERT-main/models/encryptd_vocab.txt')
    parser.add_argument('--config_path', type=str,
                        default='ET-BERT-main/bert_base_config.json')
    
    # 训练参数
    parser.add_argument('--projection_dim', type=int, default=64,
                        help='投影维度（减小以提高聚类效果）')
    parser.add_argument('--epochs', type=int, default=5)
    parser.add_argument('--batch_size', type=int, default=16)
    parser.add_argument('--learning_rate', type=float, default=5e-5,
                        help='稍高的学习率')
    parser.add_argument('--temperature', type=float, default=0.1,
                        help='更高的温度参数')
    parser.add_argument('--max_seq_length', type=int, default=128)
    parser.add_argument('--min_class_samples', type=int, default=10,
                        help='过滤掉样本数少于此值的类别')
    
    # 输出参数
    parser.add_argument('--output_model_path', type=str,
                        default='models/improved_contrastive_bert.bin')
    parser.add_argument('--output_features_path', type=str,
                        default='results/improved_contrastive_bert_features_d64.tsv')
    
    # 模型参数
    parser.add_argument('--embedding', type=str, default='word_pos_seg')
    parser.add_argument('--encoder', type=str, default='transformer')
    parser.add_argument('--mask', type=str, default='fully_visible')
    parser.add_argument('--pooling', type=str, default='first')
    parser.add_argument('--tokenizer', type=str, default='bert')
    
    args = parser.parse_args()
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")
    
    # 加载配置
    args = load_hyperparam(args)
    
    # 设置必要属性
    args.spm_model_path = None
    args.remove_embedding_layernorm = False
    args.remove_transformer_bias = False
    args.layernorm_positioning = "post"
    args.layernorm = "normal"
    args.relative_position_embedding = False
    args.relative_attention_buckets_num = 32
    args.remove_attention_scale = False
    args.parameter_sharing = False
    args.factorized_embedding_parameterization = False
    args.feed_forward = "dense"
    args.has_residual_attention = True
    args.bidirectional = False
    args.attention_head_size = args.hidden_size // args.heads_num
    args.seq_length = args.max_seq_length
    
    if not hasattr(args, 'device'):
        args.device = device
    
    # 构建分词器
    args.tokenizer = str2tokenizer[args.tokenizer](args)
    
    print("Loading data...")
    df = pd.read_csv(args.input_features_path, sep='\t')
    
    # 筛选未知样本
    df_filtered = df[df['original_ground_truth_name'].notna() & (df['original_ground_truth_name'] != '')]
    if 'predicted_label' in df_filtered.columns:
        df_filtered = df_filtered[df_filtered['predicted_label'] == 1].copy()
    
    if df_filtered.empty:
        print("No valid unknown samples found. Exiting.")
        return
    
    # 提取数据
    texts = df_filtered['original_text_a'].tolist()
    ground_truth_names = df_filtered['original_ground_truth_name'].tolist()
    
    # 过滤掉样本数太少的类别
    label_counts = Counter(ground_truth_names)
    valid_classes = {cls for cls, count in label_counts.items() if count >= args.min_class_samples}
    
    if len(valid_classes) < len(label_counts):
        print(f"Filtering out {len(label_counts) - len(valid_classes)} classes with < {args.min_class_samples} samples")
        mask = [cls in valid_classes for cls in ground_truth_names]
        texts = [t for t, m in zip(texts, mask) if m]
        ground_truth_names = [g for g, m in zip(ground_truth_names, mask) if m]
        df_filtered = df_filtered[mask].reset_index(drop=True)
    
    # 编码标签
    label_encoder = LabelEncoder()
    labels = label_encoder.fit_transform(ground_truth_names)
    num_classes = len(label_encoder.classes_)
    
    # 设置标签数量和其他参数
    args.labels_num = num_classes
    args.soft_targets = False
    args.soft_alpha = 0.5
    
    print(f"Loaded {len(texts)} samples with {num_classes} classes")
    print(f"Classes: {label_encoder.classes_}")
    
    # 分析类别分布
    final_label_counts = Counter(ground_truth_names)
    print("\nFinal class distribution:")
    for class_name, count in sorted(final_label_counts.items()):
        print(f"  {class_name}: {count} samples")
    
    # 创建数据集
    dataset = read_contrastive_dataset(args, texts, labels)
    dataloader = DataLoader(dataset, batch_size=args.batch_size, shuffle=True, drop_last=True, collate_fn=collate_fn_contrastive)
    
    # 创建模型
    print("Loading pretrained ET-BERT model...")
    model = ImprovedContrastiveBERT(args, args.projection_dim)
    
    # 加载预训练权重
    load_or_initialize_parameters(args, model)
    model = model.to(device)
    print("Pretrained ET-BERT model loaded successfully")
    
    # 设置优化器和损失函数
    criterion = InfoNCELoss(temperature=args.temperature)
    optimizer = optim.AdamW(model.parameters(), lr=args.learning_rate, weight_decay=0.01)
    
    # 训练模型
    print("Starting improved contrastive learning training...")
    train_model(model, dataloader, criterion, optimizer, device, args.epochs)
    
    # 保存模型
    os.makedirs(os.path.dirname(args.output_model_path), exist_ok=True)
    torch.save(model.state_dict(), args.output_model_path)
    print(f"Model saved to {args.output_model_path}")
    
    # 提取特征
    print("Extracting features...")
    eval_dataloader = DataLoader(dataset, batch_size=args.batch_size, shuffle=False, collate_fn=collate_fn_contrastive)
    bert_features, projected_features = extract_features(model, eval_dataloader, device)
    
    print(f"BERT features shape: {bert_features.shape}")
    print(f"Projected features shape: {projected_features.shape}")
    
    # 检查特征质量
    if np.isnan(projected_features).any():
        print("⚠️  Warning: NaN values detected in features!")
    else:
        print("✅ Features look good - no NaN values")
    
    # 保存特征
    df_output = df_filtered.copy()
    df_output['embedding'] = ['[' + ', '.join(map(str, feat)) + ']' for feat in projected_features]
    df_output.to_csv(args.output_features_path, sep='\t', index=False)
    
    print(f"Features saved to {args.output_features_path}")
    print(f"\nNext step: Run clustering with:")
    print(f"python ET-BERT-main/cluster_analysis.py --input_features_path {args.output_features_path}")

if __name__ == '__main__':
    main()
