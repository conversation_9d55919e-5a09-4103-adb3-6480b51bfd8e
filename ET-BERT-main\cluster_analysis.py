import argparse
import numpy as np
import pandas as pd
from sklearn.metrics import adjusted_rand_score, silhouette_score
from collections import Counter
import os
import umap
import hdbscan
import ast

def calculate_purity(y_true, y_pred):
    """
    Calculates the purity score for the clustering results.
    """
    contingency_matrix = pd.crosstab(y_pred, y_true)
    return np.sum(np.amax(contingency_matrix.values, axis=1)) / np.sum(contingency_matrix.values)


def main():
    parser = argparse.ArgumentParser(description="Perform UMAP + HDBSCAN clustering on pre-filtered unknown samples.")
    parser.add_argument("--input_features_path", type=str, required=True, help="Path to the .tsv file containing evaluation results and extracted features (embeddings).")

    # UMAP parameters
    parser.add_argument("--umap_dims", type=int, default=64, help="UMAP: The number of dimensions to reduce to.")
    parser.add_argument("--umap_neighbors", type=int, default=15, help="UMAP: The size of local neighborhood (in terms of number of neighboring sample points).")

    # HDBSCAN parameters
    parser.add_argument("--min_cluster_size", type=int, default=5, help="HDBSCAN: The minimum number of samples in a group for it to be considered a cluster.")
    parser.add_argument("--output_clustered_path", type=str, default="ET-BERT-main/results/clustered_dataset.tsv", help="Path to save the dataset with cluster labels.")

    args = parser.parse_args()

    # 1. Load data from the combined TSV file
    print("Loading data from combined TSV file...")
    try:
        # Read the TSV file
        combined_data = pd.read_csv(args.input_features_path, sep='\t')
        
        # Extract embeddings and convert from string representation to list of floats
        # Use ast.literal_eval to safely parse string representations of lists
        features = np.array(combined_data['embedding'].apply(ast.literal_eval).tolist())
        
        # The rest of the DataFrame is our dataset for ground truth and other info
        dataset = combined_data

    except FileNotFoundError as e:
        print(f"Error loading file: {e}. Please check the path.")
        return
    except KeyError as e:
        print(f"Error: Required column missing. {e}. Ensure 'embedding' column exists in {args.input_features_path}")
        return
    except Exception as e:
        print(f"An unexpected error occurred during data loading: {e}")
        return

    # 2. Extract true labels for evaluation
    true_labels = None
    if "original_ground_truth_name" in dataset.columns:
        true_labels = dataset['original_ground_truth_name']
        print("Found 'original_ground_truth_name' column for evaluation (for Purity/ARI).")
    elif "true_label" in dataset.columns:
        true_labels = dataset['true_label']
        print("Found 'true_label' column for evaluation (for Purity/ARI). Note: This is the binary label (0/1).")
    else:
        print("Error: No suitable ground truth column ('original_ground_truth_name' or 'true_label') found in the dataset. Cannot calculate purity/ARI.")
        # We can still proceed with clustering, just without purity evaluation.
            
    print(f"Loaded {len(features)} unknown samples for clustering.")
    if true_labels is not None:
        print(f"Number of unique true labels in the set: {true_labels.nunique()}")

    # 3. UMAP Dimensionality Reduction
    print(f"\nStep 1: Performing UMAP dimensionality reduction from {features.shape[1]} to {args.umap_dims} dimensions...")
    reducer = umap.UMAP(
        n_neighbors=args.umap_neighbors,
        n_components=args.umap_dims,
        min_dist=0.0, # Pack points tightly
        random_state=42,
        metric='euclidean' # Standard distance metric
    )
    embedding = reducer.fit_transform(features)
    print("UMAP reduction complete. New data shape:", embedding.shape)

    # 4. HDBSCAN Clustering
    print(f"\nStep 2: Performing HDBSCAN clustering with min_cluster_size={args.min_cluster_size}...")
    clusterer = hdbscan.HDBSCAN(
        min_cluster_size=args.min_cluster_size,
        gen_min_span_tree=True
    )
    clusterer.fit(embedding)
    cluster_labels = clusterer.labels_


    # Save the results with cluster labels
    print(f"\nSaving dataset with cluster labels to {args.output_clustered_path}...")
    output_df = dataset.copy()
    output_df['cluster_label'] = cluster_labels
    
    # Create the output directory if it doesn't exist
    output_dir = os.path.dirname(args.output_clustered_path)
    if not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)
        
    output_df.to_csv(args.output_clustered_path, sep='\t', index=False)
    print("Save complete.")


    # 5. Evaluate the clustering
    print("\nEvaluating clustering results...")
    
    # Number of clusters in labels, ignoring noise if present.
    n_clusters_ = len(set(cluster_labels)) - (1 if -1 in cluster_labels else 0)
    n_noise_ = list(cluster_labels).count(-1)

    print(f'Estimated number of clusters: {n_clusters_}')
    print(f'Estimated number of noise points: {n_noise_}')

    print("\n--- Clustering Evaluation Metrics ---")
    
    # Purity and ARI if true labels are available
    if true_labels is not None:
        # For HDBSCAN, filter out noise points for purity calculation
        non_noise_indices = cluster_labels != -1
        if np.sum(non_noise_indices) > 0:
            purity = calculate_purity(true_labels.to_numpy()[non_noise_indices], cluster_labels[non_noise_indices])
            ari = adjusted_rand_score(true_labels.to_numpy()[non_noise_indices], cluster_labels[non_noise_indices])
        else:
            purity, ari = 0, 0 # No clusters found

        print(f"Purity Score: {purity:.4f}")
        print(f"Adjusted Rand Index (ARI): {ari:.4f}")

    # Silhouette Score can only be calculated if there is more than 1 cluster found
    if n_clusters_ > 1:
        try:
            # We calculate silhouette on the reduced UMAP embedding for better performance and relevance
            silhouette = silhouette_score(embedding, cluster_labels)
            print(f"Silhouette Score (on UMAP embedding): {silhouette:.4f}")
        except ValueError as e:
            print(f"Could not compute Silhouette Score: {e}")
    elif n_clusters_ == 1:
        print("Silhouette Score is not defined for a single cluster.")
    else: # n_clusters_ == 0
        print("No clusters found. Cannot compute Silhouette Score.")

    print("-------------------------------------\n")
    
    print("Cluster distribution:")
    cluster_counts = Counter(cluster_labels)
    # Print noise first
    if -1 in cluster_counts:
        print(f"  Noise (-1): {cluster_counts[-1]:>5} samples")
    # Then print actual clusters, sorted
    for i in sorted([k for k in cluster_counts.keys() if k != -1]):
        print(f"  Cluster {i}: {cluster_counts.get(i, 0):>5} samples")


if __name__ == "__main__":
    main() 